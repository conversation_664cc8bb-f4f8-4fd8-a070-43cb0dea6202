<template>
  <div class="input-field-wrapper">
    <!-- 手动输入 -->
    <div v-if="chatMode === ChatMode.TEXT" class="keyboard-wrapper">
      <div class="voice-toggle" @click="toggleChatMode">
        <i class="iconfont icon-microphone" class-prefix="icon"></i>
      </div>
      <div class="input-box">
        <van-field
          v-model="message"
          class="input-content"
          rows="1"
          autosize
          autocomplete="off"
          inputmode="text"
          type="textarea"
          placeholder="可以问我任何问题"
          @keydown.enter.prevent="handleSend"
          @compositionstart="handleComposition"
          @compositionupdate="handleComposition"
          @compositionend="handleComposition"
          @input="handleInput"
        />
      </div>
      <!-- 模型选择按钮（键盘输入模式） -->
      <div v-if="selectedModelImageUrl" class="model-selector" @click="handleOpenModelPopup">
        <img :src="selectedModelImageUrl" alt="模型图标" class="model-icon" />
      </div>
      <!-- 发送按钮 -->
      <div
        v-if="chatMode === ChatMode.TEXT"
        class="send-icon"
        :class="{
          'not-input': message === '',
          'loading-input': isLoading || isSending,
        }"
      >
        <i v-if="!isLoading" class="iconfont icon-mobile-send" class-prefix="icon" @click="handleSend"></i>
        <i v-else class="iconfont icon-stop-circle-line" class-prefix="icon" @click="handleStop"></i>
      </div>
    </div>
    <!-- 语音输入 -->
    <div v-else class="audio-wrapper">
      <!-- 语音输入时，提示问题及语音转换的文字展示 -->
      <div v-if="isRecording" class="audio-text-box">
        <div v-if="!message" class="audio-placeholder">我在听，请说...</div>
        <div v-else ref="audioMessageRef" class="audio-message-text">{{ message }}</div>
      </div>
      <!-- 对话生成中，停止生成按钮 -->
      <div v-if="isLoading || isSending" class="audio-chat-loading" @click="handleStop">
        <i class="iconfont icon-stop-circle-line" class-prefix="icon"></i>
        <span>停止生成</span>
      </div>
      <!-- 键盘/语音切换按钮，语音输入按钮 -->
      <div class="audio-main">
        <div class="voice-toggle" @click="toggleChatMode">
          <i class="iconfont icon-mticon-keyboard" class-prefix="icon"></i>
        </div>
        <div
          class="audio-btn"
          :class="{ circle: isRecording, oval: !isRecording, audioDis: isLoading || isSending }"
          @touchstart.prevent="startRecording"
        >
          <img v-if="!isRecording" class="audio-oval-text-img" src="@/assets/img/audio-oval-text.png" alt="" />
        </div>
        <!-- 模型选择按钮（语音输入模式） -->
        <div v-if="selectedModelImageUrl" class="voice-model-selector" @click="handleOpenModelPopup">
          <img :src="selectedModelImageUrl" alt="模型图标" class="model-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, nextTick } from 'vue';
import { showToast, Field as vanField } from 'vant';
import { ChatMode } from '@/constants/chat';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useRoute } from 'vue-router';

const props = defineProps<{
  isLoading: boolean;
  isSending: boolean;
  selectedModelImageUrl?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'send'): void;
  (e: 'stop'): void;
  (e: 'open-model-popup', mode: string): void;
}>();
const route = useRoute();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
const audioMessageRef = ref();
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const message = ref(''); // 发送的对话文字
const chatMode = ref<ChatMode>(ChatMode.VOICE);
const isRecording = ref(false); // 是否录音输入
const isOnComposition = ref(false);
// const isOutOfBounds = ref(false); // 手指是否滑到对话按钮以外
function toggleChatMode() {
  chatMode.value = chatMode.value === ChatMode.TEXT ? ChatMode.VOICE : ChatMode.TEXT;
}

function handleComposition(e: CompositionEvent) {
  const { type } = e;
  isOnComposition.value = type !== 'compositionend';
}
async function setMicPermission() {
  try {
    await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
  }
}
// // 判断手指是否滑到录音按钮范围外
// function handleTouchMove(e: TouchEvent) {
//   if (!isRecording.value) return;
//   const touch = e.touches[0];
//   // 获取语音按钮元素
//   const voiceBtn = document.querySelector('.audio-btn') as HTMLElement;
//   const rect = voiceBtn.getBoundingClientRect();
//   // 判断触摸点是否在按钮区域外
//   isOutOfBounds.value =
//     touch.clientY < rect.top || touch.clientY > rect.bottom || touch.clientX < rect.left || touch.clientX > rect.right;
// }

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = (e: Error) => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    cancelRecording();
  };

  // 当数据可用时的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        message.value = streamData.data.full_text;
        await autoSendTimeout();
        await audioMessageScroll();
      }
    }
  };
};
// 开始录音
async function startRecording() {
  if (chatMode.value === ChatMode.VOICE && route.meta.pageCase) {
    route.meta.pageCase('moduleClick', 'b_smartassistant_z15ks0wo_mc');
  }
  if (props.isLoading) {
    return;
  }
  if (isRecording.value) {
    await stopRecording();
    return;
  }
  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  } else {
    showToast('请授权麦克风权限~');
  }
}
// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopRecording();
}, 2000);
// 取消录音
function cancelRecording() {
  recorder.stop();
  isRecording.value = false;
  message.value = '';
  handleInput();
}
// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null; // 重置定时器 ID
  }
  recorder.stop();
  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (message.value) {
    handleSend();
  } else {
    showToast('录音解析为空，请重新录制~');
  }
}
async function audioMessageScroll() {
  await nextTick(() => {
    if (audioMessageRef.value) {
      audioMessageRef.value.scrollTop = audioMessageRef.value.scrollHeight;
    }
  });
}
function handleSend() {
  emit('update:modelValue', message.value);
  if (message.value === '' || props.isLoading || props.isSending || isOnComposition.value) {
    return;
  }
  emit('send');
  message.value = '';
}
function handleStop() {
  emit('stop');
}
function handleInput() {
  emit('update:modelValue', message.value);
}

function handleOpenModelPopup() {
  // 传递当前输入模式
  const mode = chatMode.value === ChatMode.TEXT ? 'keyboard' : 'voice';
  emit('open-model-popup', mode);
}
onBeforeMount(async () => {
  await setMicPermission();
});
</script>

<style lang="scss" scoped>
:root {
  --input-max-height: 192px; /* 192px */
  --input-min-height: 72px /* 72px */;
}
.input-field-wrapper {
  width: 100%;
  .keyboard-wrapper {
    max-height: 224px;
    display: flex;
    align-items: flex-end;
    background: #ffffff;
    border-radius: 20px 20px 0px 0px;
    padding: 16px 23px;
    box-sizing: border-box;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    .voice-toggle {
      width: 48px;
      height: 48px;
      display: flex;
      flex-shrink: 0;
      margin-bottom: 20px;
      box-sizing: border-box;
      align-items: center;
      .iconfont {
        font-size: 48px;
        color: rgba(17, 17, 17, 0.85);
      }
    }

    .input-box {
      flex: 1;
      display: flex;
      align-items: stretch;
      margin-left: 36px;
      margin-right: 12px;
      .input-content {
        width: 100%;
        max-height: 192px;
        min-height: 88px;
        padding: 22px 18px 21px 18px !important;
        box-sizing: border-box;
        border-radius: 18px;
        background: rgba(17, 25, 37, 0.04);
        font-size: 32px;
        :deep(.van-field__control) {
          color: #3d3d3d;
          font-size: 32px;
          line-height: 44px;
          max-height: 165px;
        }
        :deep(.van-field__control::placeholder) {
          color: #999;
          font-size: 26px;
        }
      }
    }

    .model-selector {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      margin-bottom: 14px;
      position: relative;
      transition: transform 0.2s ease;
      &:active {
        transform: scale(0.95);
      }
      &::before {
        content: '';
        position: absolute;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(85, 176, 165, 0.08);
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      &:hover::before {
        opacity: 1;
      }
      .model-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
      }
    }
    .send-icon {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      background: #55b0a5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 14px;
      &.not-input {
        background: rgba(17, 17, 17, 0.1);
        .iconfont {
          color: #ffffff;
        }
      }
      &.loading-input {
        background: transparent;
        .iconfont {
          font-size: 60px;
          color: #55b0a5;
        }
      }
      .icon-mobile-send {
        font-size: 60px;
        margin-left: 4px;
        color: #fff;
      }
    }
  }
  .audio-wrapper {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background: linear-gradient(180deg, rgba(234, 248, 251, 0) 0%, #eaf8fb 44%);
    padding-bottom: 84px;
    padding-top: 40px;
    box-sizing: border-box;
    .audio-text-box {
      height: 144px;
      margin: 0px 60px 40px 60px;
      overflow: hidden;
      display: flex;
      align-items: center;
      .audio-placeholder {
        width: 100%;
        text-align: center;
        font-size: 32px;
        font-weight: 500;
        line-height: 44px;
        letter-spacing: 0px;
        color: rgba(17, 17, 17, 0.85);
        opacity: 0.45;
      }
      .audio-message-text {
        width: 100%;
        max-height: 100%;
        overflow-y: auto;
        font-size: 32px;
        font-weight: 500;
        line-height: 48px;
        text-align: center;
        letter-spacing: 0px;
        color: rgba(17, 17, 17, 0.85);
      }
    }
    .audio-chat-loading {
      width: 216px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 179px;
      background: #ffffff;
      margin: 0 auto;
      margin-bottom: 62px;
      .iconfont {
        font-size: 44px;
        color: #44d699;
      }
      span {
        margin-left: 12px;
        font-size: 32px;
        font-weight: normal;
        line-height: 48px;
        letter-spacing: 0px;
        color: rgba(17, 17, 17, 0.85);
      }
    }
    .audio-main {
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      .voice-toggle {
        width: 88px;
        height: 88px;
        position: absolute;
        left: 60px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        opacity: 1;
        background: #ffffff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
        .iconfont {
          font-size: 48px;
        }
      }
      .voice-model-selector {
        width: 88px;
        height: 88px;
        position: absolute;
        right: 60px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        opacity: 1;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        &:active {
          transform: scale(0.95);
          box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
        }
        &::after {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
          top: 0;
          left: 0;
          pointer-events: none;
        }
        .model-icon {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          object-fit: cover;
          box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
        }
      }
      .audio-btn {
        width: 240px;
        height: 140px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        flex-shrink: 0;
        transition:
          width 0.5s ease,
          height 0.5s ease,
          border-radius 0.5s ease;
        &.audioDis {
          opacity: 0.5;
        }
        &.oval {
          border-radius: 568px;
          background: conic-gradient(
            from 180deg at 50% 50%,
            #dff6ff -79deg,
            #86ffd1 21deg,
            #edffdf 178deg,
            #dff6ff 281deg,
            #86ffd1 381deg
          );
          border: 6px solid #fff;
          .audio-oval-text-img {
            width: 137px;
            height: auto;
          }
        }
        &.circle {
          width: 140px; /* 圆形的宽度 */
          height: 140px; /* 圆形的高度 */
          border-radius: 50%; /* 圆形 */
          background: conic-gradient(
            from 180deg at 50% 50%,
            #dff6ff -79deg,
            #86ffd1 21deg,
            #edffdf 178deg,
            #dff6ff 281deg,
            #86ffd1 381deg
          );
          border: 6px solid #fff;
          animation: circleRotate 2s linear infinite;
        }
      }
    }
  }
}
@keyframes circleRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
