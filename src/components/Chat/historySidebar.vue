<template>
  <div class="history-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="history-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">历史对话</div>
        <div class="close-btn" @click="handleClose">
          <img src="@/assets/img/close.png" alt="关闭" />
        </div>
      </div>
      <div class="sidebar-content">
        <div v-if="loading" class="loading">加载中...</div>
        <div v-else-if="conversationList.length === 0" class="empty-state">暂无历史会话</div>
        <div v-else class="conversation-list">
          <div
            v-for="item in conversationList"
            :key="item.conversationId"
            class="conversation-item"
            :class="{ 'current-conversation': item.conversationId === props.currentConversationId }"
            @click="handleConversationClick(item.conversationId)"
          >
            <div class="conversation-title">{{ item.title || '未命名会话' }}</div>
            <div class="conversation-info">
              <span class="message-count">{{ item.messageCount }}条对话</span>
              <span class="update-time">{{ new Date(item.addTime).toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, onMounted, onBeforeMount, nextTick } from 'vue';
import { getConversationList, getConversationDetail, type IConversationItem } from '@/apis/common';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  currentConversationId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close', 'select-conversation']);

const conversationList = ref<IConversationItem[]>([]);
const loading = ref(false);
const agentId = ref('');

// 初始化 agentId
onBeforeMount(() => {
  if (process.env.VUE_APP_ENV === 'production') {
    agentId.value = 'd974b9c4141e';
  } else {
    agentId.value = '7a313be6b9f7';
  }
});

// 获取历史会话记录
const fetchConversationList = async () => {
  try {
    loading.value = true;
    const response = await getConversationList(agentId.value);
    if (response?.list) {
      conversationList.value = response.list;
    }
  } catch (error) {
    console.log('获取历史会话记录失败');
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 处理会话点击事件
const handleConversationClick = async (conversationId: string) => {
  try {
    const response = await getConversationDetail(conversationId);
    if (response) {
      // 先发送选择事件
      emit('select-conversation', response);
      // 关闭侧边栏
      emit('close');
    }
  } catch (error) {
    console.error('获取会话详情失败:', error);
  }
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  async (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      await fetchConversationList(); // 打开侧边栏时获取历史会话记录
    } else {
      document.body.style.overflow = '';
    }
  },
);

onMounted(async () => {
  if (props.isOpen) {
    await fetchConversationList();
  }
});
</script>

<style lang="scss" scoped>
.history-sidebar-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none; /* 允许点击穿透到底层元素 */
}

.history-sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 450px; /* 更宽的历史记录侧边栏 */
  height: 100%;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1001; /* 确保侧边栏在遮罩层之上 */
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto; /* 恢复点击事件 */

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    /* 去掉分割线 */

    .title {
      margin-bottom: -20px;
      font-size: 30px;
      font-weight: 700;
      color: rgba(17, 17, 17, 0.85);
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 36px;
        height: 36px;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 14px;
    background-color: #fff;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading,
    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100px;
      color: rgba(17, 17, 17, 0.85);
      font-size: 16px;
    }

    .conversation-list {
      padding: 0 20px;
      .conversation-item {
        padding: 16px;
        border-radius: 24px;
        background-color: #fff;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 4px solid transparent;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        &.current-conversation {
          background-color: rgba(3, 229, 112, 0.1);
          border-color: rgba(3, 229, 112, 0.5);
        }

        &:hover {
          background-color: rgba(17, 17, 17, 0.05);
          border-color: rgba(17, 17, 17, 0.1);
          transform: translateY(-1px);

          &.current-conversation {
            background-color: rgba(3, 229, 112, 0.15);
            border-color: rgba(3, 229, 112, 0.5);
          }
        }

        .conversation-title {
          font-size: 26px;
          font-weight: 700;
          color: rgba(17, 17, 17, 0.85);
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .conversation-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 20px;
          color: rgba(17, 17, 17, 0.45);

          .message-count {
            border-radius: 10px;
            margin-right: 8px;
          }

          .update-time {
            font-size: 20px;
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto; /* 恢复点击事件，确保可以点击遮罩层关闭侧边栏 */
}
</style>
