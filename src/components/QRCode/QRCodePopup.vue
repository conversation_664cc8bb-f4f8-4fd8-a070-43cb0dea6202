<template>
  <div v-if="show" class="qrcode-popup">
    <div class="qrcode-popup-mask" @click="close"></div>
    <div class="qrcode-popup-content">
      <div class="qrcode-container">
        <div ref="qrcodeRef" class="qrcode"></div>
      </div>
    </div>
    <div class="qrcode-text">请使用 「美团APP」扫码下单</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import QRCode from 'qrcode';

const props = defineProps<{
  show: boolean;
  url: string;
}>();

const emit = defineEmits(['update:show']);

const qrcodeRef = ref<HTMLElement | null>(null);

const close = () => {
  emit('update:show', false);
};

const generateQRCode = async () => {
  if (!qrcodeRef.value || !props.url) return;
  try {
    // Clear previous QR code
    qrcodeRef.value.innerHTML = '';
    // Generate QR code
    const canvas = document.createElement('canvas');
    await QRCode.toCanvas(canvas, props.url, {
      width: 300,
      margin: 0,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
      scale: 10, // Increase scale for better resolution
    });
    // Add responsive styling to the canvas
    canvas.style.width = '100%';
    canvas.style.height = 'auto';
    canvas.style.display = 'block';
    qrcodeRef.value.appendChild(canvas);
  } catch (error) {
    console.error('Error generating QR code:', error);
  }
};

// Generate QR code when component is mounted and url changes
watch(() => props.url, generateQRCode, { immediate: true });
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      // Generate QR code when popup is shown
      setTimeout(generateQRCode, 0);
    }
  },
);

onMounted(() => {
  if (props.show) {
    // 使用 then 处理 Promise
    generateQRCode().catch((error) => {
      console.error('Error generating QR code on mount:', error);
    });
  }
});
</script>

<style scoped lang="scss">
.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  &-content {
    position: relative;
    width: 75%;
    max-width: 500px; /* Limit maximum width to 500px */
    aspect-ratio: 1/1;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .qrcode-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .qrcode {
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .qrcode-text {
    position: absolute;
    top: 60%;
    margin-top: 130px;
    left: 0;
    width: 100%;
    font-size: 26px;
    color: #fff;
    text-align: center;
    font-weight: 800;
    z-index: 1002;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}
</style>
