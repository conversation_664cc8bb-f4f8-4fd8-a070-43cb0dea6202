<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <button v-if="showHistoryBtn" class="history-btn" style="margin-right: 16px" @click="$emit('history')">
        <img src="@/assets/img/history.png" alt="历史" />
      </button>
      <div
        v-if="showUserAvatar && !userLoading"
        class="user-avatar"
        :style="{ backgroundColor: getRandomColor(currentMisId) }"
        @click="$emit('avatar-click')"
      >
        {{ getAvatarLetter(currentMisId) }}
      </div>
      <div v-else-if="showUserAvatar && userLoading" class="user-avatar loading-avatar">
        <span class="loading-dot"></span>
      </div>
      <img v-if="showAssistantAvatar" src="@/assets/img/avatar.png" alt="助手头像" class="avatar" />
      <div v-if="showUserAvatar" class="placeholder"></div>
    </div>
    <!-- 中间tab切换 -->
    <div class="tab-switch">
      <div class="tab-btn" :class="{ active: activeTab === 'chat' }" @click="$emit('switch-tab', 'chat')">对话</div>
      <div
        class="tab-btn"
        :class="{ active: activeTab === 'share' || activeTab === 'recommend' }"
        @click="$emit('switch-tab', currentPage === 'recommend' ? 'recommend' : 'share')"
      >
        {{ currentPage === 'recommend' ? '推荐' : '广场' }}
      </div>
    </div>
    <!-- 右侧按钮 -->
    <div class="header-right">
      <span v-if="showVoiceBtn" class="chat-voice" @click="$emit('voice')">
        <img v-if="isChatPlay" class="kaiqi-img" src="@/assets/img/langdu_kaiqi.png" alt="" />
        <i v-else class="iconfont icon-roo-sqt-laba"></i>
      </span>
      <div v-if="showAddChatBtn" class="add-chat" @click.stop="$emit('add-chat')">
        <img v-if="addChatType === 'add'" src="@/assets/img/add-message.png" alt="新建" />
        <img v-else src="@/assets/img/search.svg" alt="搜索" />
      </div>
    </div>
    <div v-if="showHeaderGrad" class="header-grad"></div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  showHistoryBtn: Boolean,
  showUserAvatar: Boolean,
  showAssistantAvatar: Boolean,
  showVoiceBtn: Boolean,
  showAddChatBtn: Boolean,
  addChatType: {
    type: String,
    default: 'add', // "add" | "search"
  },
  isChatPlay: Boolean,
  userLoading: Boolean,
  currentMisId: {
    type: String,
    default: '',
  },
  getRandomColor: {
    type: Function,
    default: () => '#ccc',
  },
  getAvatarLetter: {
    type: Function,
    default: () => '',
  },
  activeTab: {
    type: String,
    default: 'chat',
  },
  currentPage: {
    type: String,
    default: 'share',
  },
  showHeaderGrad: Boolean,
});

defineEmits(['switch-tab', 'history', 'voice', 'add-chat', 'avatar-click']);
</script>

<style scoped lang="scss">
.header {
  height: 88px;
  padding: 10px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    .history-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 68px;
      height: 68px;
      border-radius: 50%;
      background-color: #fff;
      border: none;
      cursor: pointer;
      &:active {
        background-color: #f5f5f5;
      }
      img {
        width: 40px;
        height: 40px;
      }
    }
    .user-avatar {
      width: 68px;
      height: 68px;
      border-radius: 50%;
      color: #fff;
      font-size: 32px;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      background: #bbb;
      &.loading-avatar {
        background: #eee;
        .loading-dot {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ccc;
          animation: avatar-loading 1s infinite alternate;
        }
      }
    }
    .avatar {
      width: 68px;
      height: 68px;
      border-radius: 50%;
    }
    .placeholder {
      width: 16px;
      height: 16px;
      visibility: hidden;
    }
  }
  .header-right {
    display: flex;
    align-items: center;
    gap: 32px;
    .chat-voice {
      width: 68px;
      height: 68px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: #fff;
      .iconfont {
        font-size: 40px;
      }
      .kaiqi-img {
        width: 40px;
        height: auto;
      }
    }
    .add-chat {
      width: 68px;
      height: 68px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: #fff;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
  .header-grad {
    position: absolute;
    top: 88px;
    left: 0px;
    width: 100%;
    height: 100px;
    opacity: 1;
    background: linear-gradient(180deg, #e1f5e8 0%, rgba(229, 247, 242, 0) 100%);
  }
  .tab-switch {
    display: flex;
    background-color: rgba(255, 255, 255, 0.55);
    border-radius: 30px;
    padding: 5px;
    width: 260px;
    height: 65px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden;
    box-sizing: border-box;
    max-width: 220px;
    max-height: 54px;
    min-width: 220px;
    min-height: 54px;
    .tab-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: 500;
      color: #666;
      position: relative;
      z-index: 2;
      transition: color 0.3s;
      border-radius: 25px;
      box-sizing: border-box;
      &.active {
        color: #333;
        font-weight: 600;
        background-color: #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
@keyframes avatar-loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
</style>
