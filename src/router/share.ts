import { RouteRecordRaw } from 'vue-router';
import { RouteName } from '@/constants/route-name';

const routes: Array<RouteRecordRaw> = [
  {
    path: 'share',
    component: () => import(/* webpackChunkName: "share" */ '@/pages/share/index.vue'),
    name: RouteName.SHARE,
    meta: {
      cid: 'c_share_plaza',
      keepAlive: true, // 添加缓存属性，保留滚动位置
    },
  },
  {
    path: 'post/:id',
    component: () => import(/* webpackChunkName: "post-detail" */ '@/pages/share/post-detail.vue'),
    name: RouteName.POST_DETAIL,
    meta: {
      cid: 'c_post_detail',
    },
  },
  {
    path: 'post/edit',
    component: () => import(/* webpackChunkName: "post-edit" */ '@/pages/share/post-edit.vue'),
    name: 'PostEdit',
    meta: {
      cid: 'c_post_edit',
    },
  },
  {
    path: 'user/:id?',
    component: () => import(/* webpackChunkName: "user-profile" */ '@/pages/share/Profile.vue'),
    name: RouteName.USER_PROFILE,
    meta: {
      cid: 'c_user_profile',
    },
  },
  {
    path: 'message',
    name: 'message',
    component: () => import('@/pages/share/message.vue'),
    meta: {
      cid: 'c_message',
    },
  },
  {
    path: 'search',
    // eslint-disable-next-line import/no-unresolved
    component: () => import(/* webpackChunkName: "share-search" */ '@/pages/share/search.vue'),
    name: RouteName.SHARE_SEARCH,
    meta: {
      cid: 'c_share_search',
    },
  },
  {
    path: 'search-result',
    // eslint-disable-next-line import/no-unresolved
    component: () => import(/* webpackChunkName: "share-search-result" */ '@/pages/share/search-result.vue'),
    name: RouteName.SHARE_SEARCH_RESULT,
    meta: {
      cid: 'c_share_search_result',
    },
  },
  {
    path: 'recommend',
    component: () => import(/* webpackChunkName: "share-recommend" */ '@/pages/share/recommed.vue'),
    name: RouteName.RECOMMEND,
    meta: {
      cid: 'c_share_recommend',
      keepAlive: true, // 添加缓存属性，保留滚动位置
    },
  },
];

export default routes;
