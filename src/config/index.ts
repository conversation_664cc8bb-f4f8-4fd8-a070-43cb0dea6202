const env = <'dev' | 'test' | 'product'>(process.env.VUE_APP_ENV === 'production' ? 'product' : 'test');

const baseRoute = '/xiaomeivv';

const apiHost = {
  dev: 'http://ceshi.com',
  test: 'https://aigc.ai.test.sankuai.com',
  product: 'https://aigc.sankuai.com',
};

const ssoHostList = {
  dev: 'https://ssosv.it.test.sankuai.com/sson/login',
  test: 'https://ssosv.it.test.sankuai.com/sson/login',
  product: 'https://ssosv.sankuai.com/sson/login',
};
const speechHostList = {
  dev: 'https://speech.sankuai.com',
  test: 'https://speech.sankuai.com',
  product: 'https://speech.sankuai.com',
};
const ssoClientIdList = {
  dev: '3b08c215c9',
  test: '3b08c215c9',
  product: '12d702aa62',
};

const ssoAccessEnvIdList = {
  dev: 'test',
  test: 'test',
  product: 'product',
};

export default {
  env,
  baseRoute,
  apiHost,
  ssoHost: ssoHostList[env],
  speechHost: speechHostList[env],
  ssoClientId: ssoClientIdList[env],
  ssoAccessEnv: ssoAccessEnvIdList[env],
};
