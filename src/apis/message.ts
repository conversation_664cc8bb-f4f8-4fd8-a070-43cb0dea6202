import fetchInstance from '@/lib/fetch';

// 单条消息类型
export interface IMessageItem {
  comment_content?: string; // 评论内容，仅type为comment时有
  comment_id?: string; // 评论ID，仅type为comment时有
  create_time: string; // 消息创建时间
  operator_id: string; // 操作人ID
  share_id: string; // 关联的分享ID
  type: 'like' | 'comment'; // 消息类型
}

// 获取新消息接口响应
export interface IGetNewMessagesResponse {
  status: number;
  message: string;
  data: {
    messages: IMessageItem[];
  };
}

/**
 * 获取用户的新点赞和评论消息
 * @param mis_id 用户ID
 * @param type 消息类型，可选'like'|'comment'，不传则返回所有类型
 */
export function getNewMessages(mis_id: string, type?: 'like' | 'comment') {
  const params = new URLSearchParams({ mis_id });
  if (type) params.append('type', type);
  return fetchInstance
    .fetch(`/weiwei/user/new_messages?${params.toString()}`, { method: 'GET' })
    .then((res: IGetNewMessagesResponse) => res);
}

// 获取未读消息数量接口响应
export interface IUnreadCountsResponse {
  status: number;
  message: string;
  data: {
    unread_like_count: number;
    unread_comment_count: number;
  };
}
/**
 * 获取用户未读的点赞和评论数量
 * @param mis_id 用户ID
 */
export function getUnreadCounts(mis_id: string) {
  return fetchInstance
    .fetch(`/weiwei/user/unread_counts?mis_id=${encodeURIComponent(mis_id)}`, { method: 'GET' })
    .then((res: IUnreadCountsResponse) => res);
}
// 标记消息为已读接口参数
export interface IMarkAsReadParams {
  mis_id: string;
  count_type: 'like' | 'comment';
}

// 标记消息为已读响应
export interface IMarkAsReadResponse {
  status: number;
  message: string;
  data: {
    message: string;
  };
}

/**
 * 标记用户未读消息为已读
 * @param params { mis_id, count_type }
 */
export function markMessagesAsRead(params: IMarkAsReadParams) {
  return fetchInstance
    .fetch('/weiwei/user/mark_as_read', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    })
    .then((res: IMarkAsReadResponse) => res);
}
