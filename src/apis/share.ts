import fetchInstance from '@/lib/fetch';
import { getUserInfo } from './common';

// 评论项接口
export interface ICommentItem {
  comment_id: string;
  comment_mis_id: string;
  comment_content: string;
  comment_parent_id: string | null;
  create_time: string;
  is_deleted: boolean;
  // UI相关字段，在前端处理
  avatar?: string;
  avatarColor?: string;
}

// 分享内容项接口
export interface IShareItem {
  id: string;
  content: string;
  mis_id_keyword: string;
  money_detail: number[] | string;
  order_detail: string[];
  share_identifier: string;
  shop_name: string;
  time: string;
  title: string;
  user_content: string;
  food_img_urls?: (string | null)[];
  like_count: number;
  comment_count?: number;
  comment_record?: ICommentItem[];
  dislike_count?: number;
  is_liked?: boolean; // 当前用户是否点赞
  is_disliked?: boolean; // 当前用户是否点踩
  liked_users?: string[]; // 点赞用户列表
  disliked_users?: string[]; // 点踩用户列表
}

// 分页响应接口
export interface IShareListResponse {
  code: number;
  message: string;
  data: {
    offset: number;
    batch_size: number;
    shares: IShareItem[];
    total: number;
    has_more: boolean;
    // 为了兼容性保留这些字段
    page?: number;
    page_size?: number;
    total_pages?: number;
  };
}

// 单个分享详情响应接口
export interface IShareDetailResponse {
  status: number;
  message: string;
  data: IShareItem;
}

/**
 * 获取分页的分享内容列表，按创建时间降序排序
 * @param params 分页参数
 * @returns Promise<IShareListResponse>
 */
export const getShareListByPage = async (params?: {
  offset?: number;
  batch_size?: number;
}): Promise<IShareListResponse> => {
  try {
    // 获取用户信息，提取login字段作为master_mis_id
    const userInfoResponse = await getUserInfo();

    console.log('用户信息响应:', userInfoResponse);

    // 如果没有获取到用户信息，使用本地存储的misid或者默认值
    let misId = 'default_user';

    if (userInfoResponse && userInfoResponse.login) {
      misId = userInfoResponse.login;
    } else if (userInfoResponse && userInfoResponse.data && userInfoResponse.data.login) {
      // 使用实际返回的数据结构
      misId = userInfoResponse.data.login;
    } else {
      // 尝试从本地存储获取
      const localMisId = localStorage.getItem('misid');
      if (localMisId) {
        misId = localMisId;
      }
    }

    console.log('使用的misId:', misId);

    return fetchInstance.fetch('/weiwei/sharebypage', {
      method: 'GET',
      params: {
        offset: params?.offset || 0,
        batch_size: params?.batch_size || 10,
        master_mis_id: misId,
      },
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw new Error('获取用户信息失败');
  }
};

/**
 * 获取单个分享内容的详细信息
 * @param shareId 分享内容的唯一标识符
 * @returns Promise<IShareDetailResponse>
 */
export const getShareDetail = (shareId: string, masterMisId: string): Promise<IShareDetailResponse> => {
  return fetchInstance.fetch('/weiwei/share', {
    method: 'GET',
    params: {
      share_id: shareId,
      master_mis_id: masterMisId,
    },
  });
};

/**
 * 提交评论
 * @param shareId 分享内容的唯一标识符
 * @param content 评论内容
 * @param parentId 父评论ID，如果是回复某条评论则提供
 * @returns Promise<{status: number, message: string, data: {comment_id: string}}>
 */
export const submitComment = (
  shareId: string,
  content: string,
  parentId?: string,
): Promise<{ status: number; message: string; data: { comment_id: string } }> => {
  interface ICommentRequestBody {
    share_id: string;
    content: string;
    parent_id?: string;
  }

  const requestBody: ICommentRequestBody = {
    share_id: shareId,
    content,
  };

  if (parentId) {
    requestBody.parent_id = parentId;
  }

  return fetchInstance.fetch('/weiwei/comment', {
    method: 'POST',
    body: JSON.stringify(requestBody),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 评论存储
 * @param shareId 分享内容的ID
 * @param commentMisId 评论用户的ID
 * @param commentContent 评论内容
 * @param commentParentId 父评论ID，用于回复评论时指定（可选）
 * @returns Promise<{code: number, message: string, data: {comment_id: string}}>
 */
export const storeComment = (
  shareId: string,
  commentMisId: string,
  commentContent: string,
  commentParentId?: string,
): Promise<{ code: number; message: string; data: { comment_id: string } }> => {
  const requestBody: {
    share_id: string;
    comment_mis_id: string;
    comment_content: string;
    comment_parent_id?: string;
  } = {
    share_id: shareId,
    comment_mis_id: commentMisId,
    comment_content: commentContent,
  };

  if (commentParentId) {
    requestBody.comment_parent_id = commentParentId;
  }

  return fetchInstance.fetch('/weiwei/share/comment/store', {
    method: 'POST',
    body: JSON.stringify(requestBody),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 删除评论
 * @param shareId 分享内容的ID
 * @param commentId 评论ID
 * @returns Promise<{status: number, message: string, data: {message: string}}>
 */
export const deleteComment = (
  shareId: string,
  commentId: string,
): Promise<{ status: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/comment/delete', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      comment_id: commentId,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 批量删除评论
 * @param shareId 分享内容的ID
 * @param commentIds 要删除的评论ID列表
 * @returns Promise<{status: number, message: string, data: {message: string}}>
 */
export const deleteComments = (
  shareId: string,
  commentIds: string[],
): Promise<{ status: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/comment/deletemore', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      comment_ids: commentIds,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 点赞分享内容
 * @param shareId 分享内容的唯一标识符
 * @param misId 用户ID
 * @returns Promise<{code: number, status?: number, message: string, data: {message: string}}>
 */
export const likeShare = (
  shareId: string,
  misId: string,
): Promise<{ code: number; status?: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/like', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      mis_id: misId,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 取消点赞分享内容
 * @param shareId 分享内容的唯一标识符
 * @param misId 用户ID
 * @returns Promise<{code: number, status?: number, message: string, data: {message: string}}>
 */
export const unlikeShare = (
  shareId: string,
  misId: string,
): Promise<{ code: number; status?: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/unlike', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      mis_id: misId,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 点踩分享内容
 * @param shareId 分享内容的ID
 * @param misId 用户ID
 * @returns Promise<{code: number, status?: number, message: string, data: {message: string}}>
 */
export const dislikeShare = (
  shareId: string,
  misId: string,
): Promise<{ code: number; status?: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/dislike', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      mis_id: misId,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 取消点踩分享内容
 * @param shareId 分享内容的ID
 * @param misId 用户ID
 * @returns Promise<{code: number, status?: number, message: string, data: {message: string}}>
 */
export const undislikeShare = (
  shareId: string,
  misId: string,
): Promise<{ code: number; status?: number; message: string; data: { message: string } }> => {
  return fetchInstance.fetch('/weiwei/share/undislike', {
    method: 'POST',
    body: JSON.stringify({
      share_id: shareId,
      mis_id: misId,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 删除指定的分享帖子
 * @param shareId 分享ID
 * @param misId 用户ID（必须为发布者）
 * @returns Promise<IDeleteShareResponse>
 */
export interface IDeleteShareRequest {
  share_id: string;
  mis_id: string;
}
export interface IDeleteShareResponse {
  status: number;
  message: string;
  data: {
    message: string;
  } | null;
}
export const deleteShare = (shareId: string, misId: string): Promise<IDeleteShareResponse> => {
  const body: IDeleteShareRequest = {
    share_id: shareId,
    mis_id: misId,
  };
  return fetchInstance.fetch('/weiwei/user/delete_share', {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 编辑指定分享帖子的标题或内容
 * @param shareId 分享ID
 * @param misId 用户ID（必须为发布者）
 * @param title 新标题（可选）
 * @param content 新内容（可选）
 * @returns Promise<IEditShareResponse>
 */
export interface IEditShareRequest {
  share_id: string;
  mis_id: string;
  title?: string;
  content?: string;
}
export interface IEditShareResponse {
  status: number;
  message: string;
  data: {
    message: string;
  } | null;
}
export const editShare = (
  shareId: string,
  misId: string,
  title?: string,
  content?: string,
): Promise<IEditShareResponse> => {
  const body: IEditShareRequest = {
    share_id: shareId,
    mis_id: misId,
  };
  if (title !== undefined) body.title = title;
  if (content !== undefined) body.content = content;
  return fetchInstance.fetch('/weiwei/user/edit_share', {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

/**
 * 搜索分享内容
 * @param keyword 搜索关键词
 * @param params 分页参数
 * @returns Promise<IShareListResponse>
 */
export const searchShare = async (
  keyword: string,
  params?: {
    offset?: number;
    batch_size?: number;
  },
): Promise<IShareListResponse> => {
  try {
    // 获取用户信息，提取login字段作为master_mis_id
    const userInfoResponse = await getUserInfo();

    // 如果没有获取到用户信息，使用本地存储的misid或者默认值
    let misId = 'default_user';

    if (userInfoResponse && userInfoResponse.login) {
      misId = userInfoResponse.login;
    } else if (userInfoResponse && userInfoResponse.data && userInfoResponse.data.login) {
      // 使用实际返回的数据结构
      misId = userInfoResponse.data.login;
    } else {
      // 尝试从本地存储获取
      const localMisId = localStorage.getItem('misid');
      if (localMisId) {
        misId = localMisId;
      }
    }

    return fetchInstance.fetch('/weiwei/share/search', {
      method: 'GET',
      params: {
        keyword,
        offset: params?.offset || 0,
        batch_size: params?.batch_size || 10,
        master_mis_id: misId,
      },
    });
  } catch (error) {
    console.error('搜索失败:', error);
    throw new Error('搜索失败');
  }
};

/**
 * 获取热门分享内容
 * @param params 分页参数
 * @returns Promise<IShareListResponse>
 */
export const getHotShares = async (params?: { offset?: number; batch_size?: number }): Promise<IShareListResponse> => {
  try {
    // 获取用户信息，提取login字段作为master_mis_id
    const userInfoResponse = await getUserInfo();

    // 如果没有获取到用户信息，使用本地存储的misid或者默认值
    let misId = 'default_user';

    if (userInfoResponse && userInfoResponse.login) {
      misId = userInfoResponse.login;
    } else if (userInfoResponse && userInfoResponse.data && userInfoResponse.data.login) {
      // 使用实际返回的数据结构
      misId = userInfoResponse.data.login;
    } else {
      // 尝试从本地存储获取
      const localMisId = localStorage.getItem('misid');
      if (localMisId) {
        misId = localMisId;
      }
    }

    return fetchInstance.fetch('/weiwei/plaza/hot_shares', {
      method: 'GET',
      params: {
        offset: params?.offset || 0,
        batch_size: params?.batch_size || 10,
        master_mis_id: misId,
      },
    });
  } catch (error) {
    console.error('获取热门分享失败:', error);
    throw new Error('获取热门分享失败');
  }
};

/**
 * 获取关注的用户分享内容
 * @param params 分页参数
 * @returns Promise<IShareListResponse>
 */
export const getFollowingShares = async (params?: {
  offset?: number;
  batch_size?: number;
}): Promise<IShareListResponse> => {
  try {
    // 获取用户信息，提取login字段作为master_mis_id
    const userInfoResponse = await getUserInfo();

    // 如果没有获取到用户信息，使用本地存储的misid或者默认值
    let misId = 'default_user';

    if (userInfoResponse && userInfoResponse.login) {
      misId = userInfoResponse.login;
    } else if (userInfoResponse && userInfoResponse.data && userInfoResponse.data.login) {
      // 使用实际返回的数据结构
      misId = userInfoResponse.data.login;
    } else {
      // 尝试从本地存储获取
      const localMisId = localStorage.getItem('misid');
      if (localMisId) {
        misId = localMisId;
      }
    }

    return fetchInstance.fetch('/weiwei/plaza/following_shares', {
      method: 'GET',
      params: {
        offset: params?.offset || 0,
        batch_size: params?.batch_size || 10,
        mis_id: misId,
      },
    });
  } catch (error) {
    console.error('获取关注分享失败:', error);
    throw new Error('获取关注分享失败');
  }
};
