import fetchInstance from '@/lib/fetch';
import type { IShareItem } from './share';

// 用户点赞帖子接口参数
export type IGetUserLikedSharesParams = {
  mis_id: string;
  offset?: number;
  batch_size?: number;
};

// 接口响应类型
export type IGetUserLikedSharesResponse = {
  status: number;
  message: string;
  data: {
    mis_id: string;
    offset: number;
    batch_size: number;
    total: number;
    has_more: boolean;
    liked_shares: IShareItem[];
  };
};

// 获取用户点赞的帖子
export function getUserLikedShares(params: IGetUserLikedSharesParams) {
  return fetchInstance.fetch('/weiwei/user/liked_shares', {
    method: 'GET',
    params,
  });
}

// 获取用户点踩的帖子接口
export type IGetUserDislikedSharesParams = IGetUserLikedSharesParams;
export type IGetUserDislikedSharesResponse = {
  status: number;
  message: string;
  data: {
    mis_id: string;
    offset: number;
    batch_size: number;
    total: number;
    has_more: boolean;
    disliked_shares: IShareItem[];
  };
};

export function getUserDislikedShares(params: IGetUserDislikedSharesParams) {
  return fetchInstance.fetch('/weiwei/user/disliked_shares', {
    method: 'GET',
    params,
  });
}

// 获取用户分享的帖子接口
export type IGetUserSharedSharesParams = IGetUserLikedSharesParams;
export type IGetUserSharedSharesResponse = {
  status: number;
  message: string;
  data: {
    mis_id: string;
    offset: number;
    batch_size: number;
    total: number;
    has_more: boolean;
    shared_shares: IShareItem[];
  };
};

export function getUserSharedShares(params: IGetUserSharedSharesParams) {
  return fetchInstance.fetch('/weiwei/user/shared_shares', {
    method: 'GET',
    params,
  });
}

// 关注用户
export interface IFollowUserParams {
  mis_id: string;
  guanzhu_misid: string;
}
export interface IFollowUserResponse {
  status: number;
  message: string;
  data: {
    message: string;
  };
}
export function followUser(data: IFollowUserParams) {
  return fetchInstance.fetch('/weiwei/user/follow', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 取消关注
export interface IUnfollowUserParams {
  mis_id: string;
  guanzhu_misid: string;
}
export interface IUnfollowUserResponse {
  status: number;
  message: string;
  data: {
    status: number;
    message: string;
  } | null;
}
export function unfollowUser(data: IUnfollowUserParams) {
  return fetchInstance.fetch('/weiwei/user/unfollow', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 获取关注列表
export interface IGetFollowingParams {
  mis_id: string;
  offset?: number;
  batch_size?: number;
}
export interface IGetFollowingResponse {
  status: number;
  message: string;
  data: {
    batch_size: number;
    following: Array<{
      mis_id: string;
      create_time?: string;
    }>;
    has_more: boolean;
    mis_id: string;
    offset: number;
    total: number;
  };
}
export function getFollowing(params: IGetFollowingParams) {
  return fetchInstance.fetch('/weiwei/user/following', {
    method: 'GET',
    params,
  });
}

// 获取粉丝列表
export interface IGetFollowersParams {
  mis_id: string;
  offset?: number;
  batch_size?: number;
}
export interface IGetFollowersResponse {
  status: number;
  message: string;
  data: {
    batch_size: number;
    followers: Array<{
      mis_id: string;
      create_time?: string;
    }>;
    has_more: boolean;
    mis_id: string;
    offset: number;
    total: number;
  };
}
export function getFollowers(params: IGetFollowersParams) {
  return fetchInstance.fetch('/weiwei/user/followers', {
    method: 'GET',
    params,
  });
}
