import fetchInstance from '@/lib/fetch';

// 语音转文字
export const getStreamAsr = (params: IAsrParams): Promise<IApiResponseData<IAsrRequest>> => {
  const { data, ...audioParams } = params;
  return fetchInstance.fetch('/weiwei/stream/asr', {
    method: 'POST',
    body: data, // 直接发送二进制数据
    headers: {
      'Session-Id': params.sessionId,
      'Audio-Param': btoa(JSON.stringify(audioParams)),
      'Content-Type': 'application/octet-stream',
    },
  });
};
// 单条播放-文字转语音
export const getStreamSynthesis = (params: {
  text: string;
  signal?: AbortSignal; // 添加可选的 AbortSignal
}) => {
  const { text, signal } = params;
  return fetchInstance.fetch(`/weiwei/tts/stream-synthesis`, {
    method: 'POST',
    body: JSON.stringify({
      text,
      voiceName: 'meifanli',
      speed: 50,
      volume: 50,
      sampleRate: 16000,
      audioFormat: 'mp3',
    }),
    responseType: 'blob',
    headers: {
      Accept: 'audio/mpeg',
    },
    signal, // 传递 AbortSignal
  });
};
// 自动播放-文字转语音
export const getTtsResponse = (params: {
  id: string;
  signal?: AbortSignal; // 添加可选的 AbortSignal
}) => {
  const { signal, id } = params;
  return fetchInstance.fetch('/weiwei/stream/get-tts-response', {
    method: 'GET',
    headers: {
      'tts-session-id': id,
      Accept: 'audio/mpeg',
    },
    responseType: 'blob',
    signal, // 传递 AbortSignal
  });
};
// 话题推荐
export const getTopicRecommend = (): Promise<IApiResponseData<{ list: ITopicRecommend[] }>> => {
  return fetchInstance.fetch('/weiwei/topic/recommend', {
    method: 'GET',
  });
};
// 创建新会话
export const createConversation = (params: ICreateRequest): Promise<IApiResponseData<IConversation>> => {
  return fetchInstance.fetch('/web/v1/conversation/create', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      Agentid: params.agentId,
    },
  });
};
