<template>
  <!-- 中间内容区域 -->
  <div class="v-topicRecommend-content">
    <div class="title">
      <div class="title-one">
        <span>美好的一天开始了</span>
        <img src="@/assets/img/xiaolian.png" alt="" />
      </div>
      <div class="title-tow"><span>小美喂喂</span>随时为你服务</div>
    </div>
    <div class="topic-main">
      <div v-for="(item, index) in topicRecommendList" :key="index" class="topic-item" @click="handleHaveTry(item)">
        <img :src="item.icon" :alt="item.title" class="item-img" />
        <div class="item-info">
          <div class="item-title">{{ item.title }}</div>
          <div class="item-desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getTopicRecommend } from '@/apis/chat';

const emit = defineEmits(['handleHaveTry']);
const topicRecommendList = ref<ITopicRecommend[]>([]);
const handleHaveTry = (item: ITopicRecommend) => {
  console.log(item.prompt);
  emit('handleHaveTry', item.prompt);
};
onMounted(async () => {
  const { data } = await getTopicRecommend();
  topicRecommendList.value = data.list;
});
</script>

<style scoped lang="scss">
.v-topicRecommend-content {
  width: 100%;
  height: 100%;
  padding: 127px 40px 40px 40px;
  overflow-y: auto;
  box-sizing: border-box;

  .title {
    font-size: 54px;
    font-weight: 500;
    line-height: 70px;
    margin-bottom: 47px;
    .title-one {
      color: #111111;
      img {
        width: 96px;
        height: auto;
        margin-left: 13px;
      }
    }
    .title-tow {
      color: #111111;
      span {
        color: #55b0a5;
      }
    }
  }
  .topic-main {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px 20px;
    padding-left: 17px;
    box-sizing: border-box;
  }
  .topic-item {
    width: 100%;
    height: auto;
    border-radius: 40px;
    position: relative;
    overflow: hidden;
    &:nth-child(1) {
      transform: rotate(3.12deg);
      .item-title {
        color: #ca3a4d;
      }
    }
    &:nth-child(2) {
      transform: rotate(-3.12deg);
      .item-title {
        color: #718402;
      }
    }
    &:nth-child(3) {
      transform: rotate(-3.12deg);
      .item-title {
        color: #d085ee;
      }
    }
    &:nth-child(4) {
      transform: rotate(3.12deg);
      .item-title {
        color: #a675fa;
      }
    }
    .item-img {
      width: 100%;
      height: 100%;
    }

    .item-info {
      width: 252px;
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 60px;
      left: 24px;
      .item-title {
        font-size: 24px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
      }
      .item-desc {
        margin-top: 4px;
        font-size: 28px;
        font-weight: 500;
        line-height: 40px;
        letter-spacing: 0px;
        color: rgba(17, 17, 17, 0.85);
      }
    }
  }
}
</style>
