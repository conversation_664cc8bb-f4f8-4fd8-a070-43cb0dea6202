<template>
  <div class="deep-thinking-wrapper">
    <div class="deep-thinking-box" @click="handleDeepThinking">
      <div class="deep-thinking-status">
        {{ reasoningData.status === ReasoningStatus.PROCESSING ? '思考中...' : '已深度思考' }}
      </div>
      <van-icon v-if="expanded" name="arrow-up" class="expanded-icon" />
      <van-icon v-else name="arrow-down" class="expanded-icon" />
    </div>
    <div v-show="expanded" class="deep-thinking-content">
      <div style="white-space: pre-wrap; word-break: break-word">{{ handleDeepThinkingContent }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs } from 'vue';
import { Icon as vanIcon } from 'vant';
import { ReasoningStatus } from '@/constants/chat';

interface IProps {
  reasoningData: IReasoningData;
}

const props = defineProps<IProps>();
const { reasoningData } = toRefs(props);
const expanded = ref(true);
const handleDeepThinkingContent = computed(() => {
  if (reasoningData.value.content.startsWith('\n')) {
    return reasoningData.value.content.replace(/^\n/, '');
  }

  return handleCitationFormat(reasoningData.value.content);
});
// 格式化引用: [citation:1] -> [1]
const handleCitationFormat = (content = '') => {
  return content.replace(/\[\s*citation:(\d+)\]/g, '[$1]');
};
const handleDeepThinking = () => {
  expanded.value = !expanded.value;
};
</script>

<style scoped lang="scss">
.deep-thinking-box {
  width: fit-content;
  display: flex;
  border-radius: 10px;
  justify-content: space-between;
  align-items: center;
  padding: 0px 16px;
  font-size: 24px;
  cursor: pointer;
  background-color: rgba(17, 25, 37, 0.03);
  margin-bottom: 20px;
  .deep-thinking-status {
    margin-right: 6px;
    padding: 0;
    color: #000;
    line-height: 48px;
  }
  .expanded-icon {
    font-size: 24px;
  }
}

.deep-thinking-content {
  color: #8b8b8b;
  font-size: 24px;
  border-left: 2px solid #e5e5e5;
  padding-left: 10px;
  margin-bottom: 20px;
  line-height: 48px;
}
</style>
