<template>
  <div class="chat-message" :class="{ 'is-user': messageData.role === 'user' }">
    <!-- 调试信息 -->
    <div v-if="false" style="font-size: 12px; color: #999">
      <pre>{{ JSON.stringify(messageData, null, 2) }}</pre>
    </div>
    <LoadingTip
      v-if="!messageData.content && !messageData.reasoningData?.content && messageData.role === 'assistant'"
    />
    <div class="message-container">
      <!-- 消息内容上方展示AI推理依据 -->
      <PretoolsCard
        v-if="messageData.role === 'assistant' && messageData.pre_tools && messageData.pre_tools.length"
        :pretools="messageData.pre_tools"
      />
      <div class="message-content">
        <template v-if="messageData.content || messageData.reasoningData.content">
          <template v-if="messageData.role === 'assistant'">
            <DeepThinking
              v-if="messageData.reasoningData.content"
              :reasoning-data="messageData.reasoningData"
            ></DeepThinking>
            <messageRender v-if="messageData.content" :text="messageData.content"></messageRender>
          </template>
          <span v-else>{{ messageData.content }}</span>
        </template>
        <div v-else class="loading">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <!-- 操作按钮 (仅在 role=assistant 时显示) -->
        <div
          v-if="messageData.isFinish && messageData.role === 'assistant' && messageData.content"
          class="action-buttons"
        >
          <div class="action-btn" @click="handlePlay">
            <i
              v-if="!isCurrentAudioPlaying(messageData.debugInfo?.id || '') || audioStatus === 'completed'"
              class="iconfont icon-roo-sqt-laba"
            ></i>
            <template v-else>
              <i v-if="audioStatus === 'loading'" class="iconfont icon-sg-loading"></i>
              <img v-else class="kaiqi-img" src="@/assets/img/langdu_kaiqi.png" alt="" />
            </template>
          </div>
          <div class="action-btn" @click="handleCopy">
            <i class="iconfont icon-mtdui-copy-o"></i>
          </div>
          <div v-if="isRegenerate" class="action-btn" @click="handleRegenerate">
            <i class="iconfont icon-sg-refresh"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';
import messageRender from '@/components/Chat/messageRender.vue';
import { copyToClipboard } from '@/utils';
import DeepThinking from './DeepThinking.vue';
import LoadingTip from './LoadingTip.vue';
import PretoolsCard from './PretoolsCard.vue';
import { useAudioQueue } from '../useAudioPlayer';

const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();

interface IProps {
  messageData: IChatStreamContent;
  isRegenerate: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['regenerate']);

// 使用 watch 监听 messageData 的变化，在数据更新后输出日志
watch(
  () => props.messageData,
  (newVal) => {
    console.log('消息数据更新:', newVal);
    console.log('是否有 pre_tools:', newVal?.pre_tools);
    console.log('pre_tools 类型:', newVal?.pre_tools ? typeof newVal.pre_tools : 'undefined');
    console.log('pre_tools 长度:', newVal?.pre_tools?.length);
  },
  { deep: true },
);
const handlePlay = () => {
  console.log(11);
  if (isCurrentAudioPlaying(props.messageData.debugInfo?.id || '')) {
    stop();
  } else {
    play({
      id: props.messageData.debugInfo?.id || '',
      text: props.messageData.content,
      type: 'manualPlay',
    });
  }
};
const handleRegenerate = () => {
  emit('regenerate', props.messageData);
};
const handleCopy = () => {
  copyToClipboard(props.messageData.content);
};
</script>

<style scoped lang="scss">
.chat-message {
  width: 100%;
  margin: 22px 0px;
  box-sizing: border-box;
  position: relative;

  .message-container {
    display: flex;
    flex-direction: column;

    .message-content {
      width: fit-content;
      padding: 32px;
      background: #fff;
      border-radius: 40px 40px 40px 8px;
      font-size: 32px;
      font-weight: normal;
      line-height: 48px;
      letter-spacing: 0px;
      color: rgba(17, 17, 17, 0.85);

      &.loading-content {
        border-radius: 20px 20px 20px 0px;
      }

      .loading {
        display: flex;
        gap: 4px;
        align-items: center;
        padding: 6px;

        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: #86ffd1;
          opacity: 0.3;
          animation: dotFade 1.4s infinite;

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
  }

  &.is-user {
    margin-left: auto;

    .message-container {
      flex-direction: row-reverse;

      .message-content {
        font-weight: 500;
        color: rgba(17, 17, 17, 0.85);
        background: #94f4c6;
        border-radius: 40px 40px 8px 40px;
      }
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 36px;
    color: #666;
    padding-top: 32px;

    .action-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      font-size: 40px;

      .icon-sg-loading {
        color: #86ffd1;
        animation: loadingSpin 2s linear infinite;
      }

      .kaiqi-img {
        width: 40px;
        height: auto;
      }
    }
  }
}

@keyframes loadingSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
