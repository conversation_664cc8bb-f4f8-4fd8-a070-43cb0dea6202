<template>
  <div class="v-chat-container">
    <div class="v-chat" style="position: relative">
      <!-- 头部区域改为Maintopbar组件 -->
      <Maintopbar
        :show-history-btn="true"
        :show-user-avatar="false"
        :show-assistant-avatar="true"
        :show-voice-btn="true"
        :show-add-chat-btn="true"
        add-chat-type="add"
        :is-chat-play="isChatPlay"
        :user-loading="false"
        :current-mis-id="''"
        :get-random-color="() => '#ccc'"
        :get-avatar-letter="() => ''"
        :active-tab="activeTab"
        :show-header-grad="isScrollDown"
        @switch-tab="switchTab"
        @history="toggleHistorySidebar"
        @voice="handleChatPlay"
        @add-chat="handleNewChat"
      />
      <div></div>
      <!-- 中间内容区域 -->
      <div v-if="chatMessageList.length" class="chat-content">
        <div ref="scrollWrapper" class="chat-scroll-wrapper" @scroll="checkScrollBottom">
          <template v-for="(item, index) in chatMessageList" :key="item.key">
            <ChatItem
              :message-data="item"
              :is-regenerate="index === chatMessageList.length - 1"
              @regenerate="handleRegenerate"
            ></ChatItem>
          </template>
        </div>
      </div>
      <!-- 推荐内容区域-->
      <div v-else class="topic-recommend">
        <TopicRecommend @handle-have-try="handleHaveTry"></TopicRecommend>
      </div>
      <!-- 底部输入框 -->
      <div class="footer">
        <form class="input-wrapper" action="" @submit="handleInputSend()">
          <InputField
            ref="inputFieldRef"
            v-model="message"
            :is-loading="answerStatus === AnswerStatusEnum.LOADING"
            :is-sending="answerStatus === AnswerStatusEnum.SENDING"
            :selected-model-image-url="selectedModelImageUrl"
            @send="handleInputSend"
            @stop="handleStop"
            @open-model-popup="handleOpenModelPopup"
          />
        </form>
      </div>
      <!-- 模型选择弹窗 -->
      <ModelPopup
        :show="showModelPopup"
        :model-list="modelList"
        :selected-model="selectedModel"
        :input-mode="currentInputMode"
        @close="showModelPopup = false"
        @select-model="handleModelSelect"
      />
      <!-- 历史对话侧边栏 -->
      <HistorySidebar
        :is-open="showHistorySidebar"
        :current-conversation-id="conversationId"
        @close="showHistorySidebar = false"
        @select-conversation="handleSelectConversation"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Maintopbar from '@/components/Maintopbar.vue';
import { ref, nextTick, onBeforeMount, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import InputField from '@/components/Chat/inputField.vue';
import { AnswerStatusEnum, ReasoningStatus, ChatMode } from '@/constants/chat';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useChatStore } from '@/stores/chat';
import { createConversation } from '@/apis/chat';
import {
  getModelList,
  getConversationList,
  editConversationTitle,
  type IConversationDetail,
  type IMessageItem,
} from '@/apis/common';
import { Popup as vanPopup, showSuccessToast } from 'vant';
import { Typewriter } from '@/utils/typeWriter';
import { debounce } from 'lodash-es';
import { generateRandomString } from '@/utils';
import { getAccessToken } from '@/lib/fetch';
import HistorySidebar from '@/components/Chat/historySidebar.vue';
import { useRouter, useRoute } from 'vue-router';
import ModelPopup from './components/ModelPopup.vue';
import { useAudioQueue, IAudioType } from './useAudioPlayer';
import ChatItem from './components/chatItem.vue';
import TopicRecommend from './components/topicRecommend.vue';

// 模型数据类型定义
interface IModelInfo {
  model: string;
  label: string;
  description: string;
  maxTokenCount: number;
  imageUrl: string;
}
const { play, stop, isPlaying } = useAudioQueue();

const router = useRouter();
const route = useRoute();
const activeTab = ref('chat');

// 切换标签
const switchTab = (tab: string) => {
  activeTab.value = tab;
  void router.push(`/${tab}`);
};

// 初始化活动标签
onMounted(() => {
  const { path } = route;
  if (path.includes('/share')) {
    activeTab.value = 'share';
  } else {
    activeTab.value = 'chat';
  }
});

let curRequestController: AbortController | null = null;
const typewriter = new Typewriter(async (str: string) => {
  if (str) {
    chatMessageList.value[chatMessageList.value.length - 1].content = str;
    await nextTick(() => {
      scrollToEnd();
    });
  }
});
const reasoningTypewriter = new Typewriter(async (str: string) => {
  if (str) {
    chatMessageList.value[chatMessageList.value.length - 1].reasoningData.content = str;
    await nextTick(() => {
      scrollToEnd();
    });
  }
});
const chatStore = useChatStore();
const { answerStatus } = storeToRefs(chatStore);
const scrollWrapper = ref();
const isScrollDown = ref(false);
const isChatPlay = ref(false);
const message = ref('');
const currentInputMode = ref('keyboard');
// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const inputFieldRef = ref<InstanceType<typeof InputField> | null>(null);
const conversationId = ref('');
const chatMessageList = ref<IChatStreamContent[]>([]);

// 模型相关数据
const modelList = ref<Record<string, IModelInfo>>({});
const selectedModel = ref('');
const selectedModelLabel = ref('');
const selectedModelImageUrl = ref('');
const showModelPopup = ref(false);

// 历史侧边栏相关
const showHistorySidebar = ref(false);

// 打开模型选择弹窗
const handleOpenModelPopup = (mode?: string) => {
  showModelPopup.value = true;
  // 如果有传入模式参数，直接使用
  if (mode) {
    currentInputMode.value = mode;
  }
};

// 选择模型
const selectModel = (modelName: string, modelLabel: string, imageUrl?: string) => {
  selectedModel.value = modelName;
  selectedModelLabel.value = modelLabel;
  selectedModelImageUrl.value = imageUrl || '';
  createRequestData.value.model = modelName;
  showModelPopup.value = false;
  showSuccessToast(`已切换到${modelLabel}`);
};

// 处理模型选择（从ModelPopup组件接收）
const handleModelSelect = (data: { modelName: string; modelLabel: string; imageUrl?: string }) => {
  selectModel(data.modelName, data.modelLabel, data.imageUrl);
};

const createRequestData = ref<ICreateRequest>({
  model: selectedModel.value,
  welcomeMessageSwitch: false,
  agentId: '',
});
const getSendQuery = (query: { content: string; regenerate: number }) => {
  return {
    content: query.content,
    conversationId: conversationId.value,
    model: createRequestData.value.model,
    regenerate: query.regenerate,
    requireAction: true,
    userSelectPluginCode: '',
  };
};
const handleChatPlay = () => {
  isChatPlay.value = !isChatPlay.value;
  if (!isChatPlay.value) {
    stop();
  }
};

// 新增新建会话方法
const handleNewChat = () => {
  // 如果有语音在播放就停止播放
  if (isPlaying) {
    stop();
  }
  // 清空消息列表
  chatMessageList.value = [];
  // 清空会话ID
  conversationId.value = '';
  showSuccessToast('新建会话成功');
};
// 终止会话
const handleStop = () => {
  handleChatError('本次回答已被终止', AnswerStatusEnum.CANCEL);
};
// 发送
const handleInputSend = async () => {
  // 如果没有conversationId，先创建新会话
  if (!conversationId.value || conversationId.value === '') {
    const { data } = await createConversation(createRequestData.value);
    conversationId.value = data.conversationId || '';
    chatStore.setConversationInfo(data);
    // 修改会话标题为用户第一条消息的前10个字
    if (conversationId.value && message.value) {
      const title = message.value.slice(0, 10);
      await editConversationTitle({
        conversationId: conversationId.value,
        title,
      });
    }
  }

  chatMessageList.value.push({
    role: 'user',
    content: message.value,
    key: generateRandomString(32),
    isFinish: true,
    reasoningData: {} as IReasoningData,
  });
  // 如果有语音在播放就停止播放
  if (isPlaying) {
    stop();
  }
  await nextTick(() => {
    scrollToEnd();
  });
  const mes = message.value;
  message.value = '';
  const sendData = getSendQuery({ content: mes, regenerate: 0 });

  await sendMessage(sendData);
};
// 发送信息请求接口
const sendMessage = async (sendData: IChatRequest) => {
  try {
    chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);
    // Ensure chatMessageList is initialized before pushing
    if (!chatMessageList.value) {
      chatMessageList.value = [];
    }
    chatMessageList.value.push({
      role: 'assistant',
      content: '',
      isFinish: false,
      reasoningData: {
        content: '',
        status: '',
      },
      key: generateRandomString(32),
    });
    await nextTick(() => {
      scrollToEnd();
    });
    let count = 0; // 收到的包的数量
    const url = `/web/v1/stream/chat`;
    curRequestController = new AbortController();

    await fetchEventSource(url, {
      method: 'post',
      credentials: 'same-origin',
      signal: curRequestController.signal,
      body: JSON.stringify(sendData),
      headers: {
        'Content-Type': 'application/json',
        'access-token': getAccessToken(),
      },
      openWhenHidden: true,
      async onmessage(msg) {
        try {
          if (msg.event === 'FatalError') {
            throw new Error(msg.data);
          }
          const data: IChatMessage = JSON.parse(msg.data);
          if (count === 0) {
            getTtsResponseData({
              id: data.id,
              text: '',
              type: 'autoPlay',
            });
          }
          if (data.lastOne) {
            await handleSandEnd(data);
          } else {
            // 保存 pre_tools 字段
            if (data.pre_tools) {
              chatMessageList.value[chatMessageList.value.length - 1].pre_tools = data.pre_tools;
            }
            handleMessage(data.content || '', count);
            handleReasoningMessage(data.reasoning_content as string, count, data.reasoning_status as string);
            count++;
          }
        } catch (msgError) {
          console.error('Message processing error:', msgError);
          handleChatError('消息处理失败，请重试');
          throw msgError; // Re-throw to trigger onclose
        }
      },
      onerror(err: Error) {
        console.error('Stream error:', err);
        if (chatMessageList.value && chatMessageList.value.length > 0) {
          handleChatError('接口访问失败，请重试');
        }
        throw err; // Re-throw to trigger onclose and stop the stream
      },
      onclose() {
        // Clean up connection state
        chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        if (curRequestController) {
          curRequestController = null;
        }
      },
    });
  } catch (error) {
    console.error('Send message error:', error);
    handleChatError('发送消息失败，请重试');
    // Ensure controller is cleaned up
    if (curRequestController) {
      curRequestController = null;
    }
  }
};
const handleChatError = (errorText: string, status: AnswerStatusEnum = AnswerStatusEnum.FAIL) => {
  curRequestController?.abort();
  chatStore.setAnswerStatus(status);
  typewriter.add(errorText);
  typewriter.done();
  reasoningTypewriter.done();
  chatMessageList.value[chatMessageList.value.length - 1].isFinish = true;
  chatMessageList.value[chatMessageList.value.length - 1].debugInfo = {
    id: generateRandomString(32),
  };
  getTtsResponseData({
    id: chatMessageList.value[chatMessageList.value.length - 1].debugInfo?.id as string,
    text: chatMessageList.value[chatMessageList.value.length - 1].content,
    type: 'manualPlay',
  });
};
// 会话接口流结束
const handleSandEnd = async (data: IChatMessage) => {
  // 保存消息数据
  chatMessageList.value[chatMessageList.value.length - 1].debugInfo = data;
  chatMessageList.value[chatMessageList.value.length - 1].isFinish = true;

  // 保存 pre_tools 字段（如果存在）
  if (data.pre_tools) {
    console.log('最终消息中保存 pre_tools 字段:', data.pre_tools);
    chatMessageList.value[chatMessageList.value.length - 1].pre_tools = data.pre_tools;
  }

  typewriter.done();
  reasoningTypewriter.done();
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  await nextTick(() => {
    scrollToEnd();
  });
};
// 深度思考
const handleReasoningMessage = (content: string, count: number, reasoningStatus: string) => {
  if (count === 1) {
    reasoningTypewriter.start();
  }
  if (
    content &&
    chatMessageList.value[chatMessageList.value.length - 1].reasoningData.status !==
      (ReasoningStatus.FINISHED as string)
  ) {
    chatMessageList.value[chatMessageList.value.length - 1].reasoningData.status = reasoningStatus || '';
    reasoningTypewriter.add(content);
    if (reasoningStatus === (ReasoningStatus.FINISHED as string)) {
      reasoningTypewriter.done();
    }
  }
};
const handleMessage = (content: string, count: number) => {
  if (count === 1) {
    chatStore.setAnswerStatus(AnswerStatusEnum.SENDING);
    typewriter.start();
  }
  if (content) {
    typewriter.add(content);
  }
};
const scrollToEnd = debounce(() => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTo({
      top: scrollWrapper.value.scrollHeight,
      behavior: 'smooth',
    });
  }
}, 100);

// 滚动事件
const checkScrollBottom = () => {
  if (scrollWrapper.value) {
    if (scrollWrapper.value.scrollTop + scrollWrapper.value.clientHeight >= scrollWrapper.value.scrollHeight) {
      isScrollDown.value = false;
    } else {
      isScrollDown.value = true;
    }
  }
};
// 刷新对话
const handleRegenerate = async (chatItem: IChatStreamContent) => {
  // 找到当前对话在列表中的索引
  const currentIndex = chatMessageList.value.findIndex((item) => item.key === chatItem.key);
  console.log(currentIndex);
  if (currentIndex === -1) return;

  // 获取对应的用户输入消息
  const userMessage = chatMessageList.value[currentIndex - 1];
  if (!userMessage || userMessage.role !== 'user') return;

  // 删除当前的助手回复
  chatMessageList.value.splice(currentIndex, 1);

  // 重新发送消息
  const sendData = getSendQuery({
    content: userMessage.content,
    regenerate: 1, // 标记为重新生成
  });
  await sendMessage(sendData);
};
const handleHaveTry = async (val: string) => {
  try {
    // 如果没有conversationId，先创建新会话
    if (!conversationId.value || conversationId.value === '') {
      const { data } = await createConversation(createRequestData.value);
      if (!data || !data.conversationId) {
        throw new Error('创建会话失败');
      }
      conversationId.value = data.conversationId;
      chatStore.setConversationInfo(data);
      // 修改会话标题为推荐提示的前10个字
      if (val) {
        const title = val.slice(0, 10);
        try {
          await editConversationTitle({
            conversationId: conversationId.value,
            title,
          });
        } catch (titleError) {
          // 继续执行，因为标题修改失败不影响主要功能
        }
      }
    }

    chatMessageList.value.push({
      role: 'user',
      content: val,
      key: Date.now(),
      isFinish: false,
      reasoningData: {} as IReasoningData,
    });
    await sendMessage(
      getSendQuery({
        content: val,
        regenerate: 0,
      }),
    );
  } catch (error) {
    handleChatError('发送消息失败，请重试');
    // 如果是新会话创建失败，清空conversationId
    if (!conversationId.value) {
      chatStore.clearConversationInfo();
    }
  }
};
// 会话语音
const getTtsResponseData = (data: { id: string; text: string; type: IAudioType }) => {
  if (isChatPlay.value) {
    play({
      id: data?.id || '',
      text: data?.text || '',
      type: data.type,
    });
  }
};
// 切换历史侧边栏显示状态
const toggleHistorySidebar = () => {
  showHistorySidebar.value = !showHistorySidebar.value;
};

// 处理会话选择
const handleSelectConversation = async (data: IConversationDetail) => {
  // 更新会话ID
  conversationId.value = data.conversationId;
  // 清空消息列表
  chatMessageList.value = [];
  // 添加历史消息
  const messages = data.messageList.map((msg: IMessageItem) => ({
    role: msg.role,
    content: msg.content,
    key: generateRandomString(),
    isFinish: true,
    reasoningData: {
      content: msg.reasoning_content || '',
      status: msg.reasoning_content ? ReasoningStatus.FINISHED : '',
    },
    debugInfo: {
      id: generateRandomString(),
    },
  }));
  chatMessageList.value = messages;

  // 确保模型列表已加载
  await loadModelList();

  // 根据最后一条消息的model值更新当前选中的模型
  if (data.messageList.length > 0) {
    const lastMessage = data.messageList[data.messageList.length - 1];
    if (lastMessage.model) {
      // 从 modelList 中获取对应模型的信息
      const modelInfo = modelList.value[lastMessage.model];
      if (modelInfo) {
        // 使用 selectModel 函数更新所有相关值
        selectModel(lastMessage.model, modelInfo.label, modelInfo.imageUrl);
      } else {
        // 仅更新模型名称
        selectedModel.value = lastMessage.model;
        createRequestData.value.model = lastMessage.model;
      }
    }
  }

  // 滚动到底部
  try {
    scrollToEnd();
  } catch (error) {
    console.log('滚动到底部失败');
  }
};

// 获取并处理模型列表数据
const loadModelList = async () => {
  try {
    const modelListResponse = await getModelList();
    // 处理模型列表数据
    if (modelListResponse && typeof modelListResponse === 'string') {
      try {
        const modelData = JSON.parse(modelListResponse) as Record<string, IModelInfo>;
        modelList.value = modelData;
        // 如果当前选中的模型在列表中存在，更新其标签
        if (modelData[selectedModel.value]) {
          selectedModelLabel.value = modelData[selectedModel.value].label;
          selectedModelImageUrl.value = modelData[selectedModel.value].imageUrl || '';
        } else if (Object.keys(modelData).length > 0) {
          // 如果当前选中的模型不在列表中，选择列表中的第一个模型
          const firstModelKey = Object.keys(modelData)[0];
          const firstModel = modelData[firstModelKey];
          selectedModel.value = firstModel.model;
          selectedModelLabel.value = firstModel.label;
          selectedModelImageUrl.value = firstModel.imageUrl || '';
          createRequestData.value.model = firstModel.model;
        }
      } catch (parseError) {
        console.error('解析模型列表数据失败:', parseError);
      }
    }
  } catch (error) {
    console.error('获取模型列表失败：', error);
  }
};

onBeforeMount(async () => {
  if (process.env.VUE_APP_ENV === 'production') {
    createRequestData.value.agentId = 'd974b9c4141e';
  } else {
    createRequestData.value.agentId = '7a313be6b9f7';
  }
  await loadModelList();
});
</script>

<style scoped lang="scss">
.v-chat-container {
  width: 100%;
  height: 100vh;
}

.v-chat {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url(@/assets/img/page-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.header {
  height: 88px;
  padding: 10px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .history-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 68px;
      height: 68px;
      border-radius: 50%;
      background-color: #fff;
      border: none;
      cursor: pointer;

      &:active {
        background-color: #f5f5f5;
      }
      img {
        width: 40px;
        height: 40px;
      }
    }
  }

  .avatar {
    width: 68px;
    height: 68px;
    border-radius: 50%;
  }
  .header-right {
    display: flex;
    align-items: center;
    gap: 32px;

    @mixin heaerIcon {
      width: 68px;
      height: 68px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: #fff;
    }
    .agent-check-box {
      display: flex;
      align-items: center;
      .agent-check-label {
        font-size: 24px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;
        color: rgba(17, 17, 17, 0.45);
        margin-right: 12px;
      }
      .agent-check {
        font-size: 40px;
      }
    }

    .chat-voice {
      @include heaerIcon;
      .iconfont {
        font-size: 40px;
      }
      .kaiqi-img {
        width: 40px;
        height: auto;
      }
    }
    .add-chat {
      @include heaerIcon;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
  .header-grad {
    position: absolute;
    top: 88px;
    left: 0px;
    width: 100%;
    height: 100px;
    opacity: 1;
    background: linear-gradient(180deg, #e1f5e8 0%, rgba(229, 247, 242, 0) 100%);
  }

  // 切换按钮样式
  .tab-switch {
    display: flex;
    background-color: rgba(255, 255, 255, 0.55);
    border-radius: 30px;
    padding: 5px;
    width: 220px !important;
    height: 54px !important;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden;
    box-sizing: border-box;
    max-width: 220px;
    max-height: 54px;
    min-width: 220px;
    min-height: 54px;

    .tab-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: 500;
      color: #666;
      position: relative;
      z-index: 2;
      transition: color 0.3s;
      border-radius: 25px;
      box-sizing: border-box;

      &.active {
        color: #333;
        font-weight: 600;
        background-color: #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.chat-content {
  flex: 1;
  padding: 0px 0px 32px 0px;
  box-sizing: border-box;
  overflow: hidden;
  .chat-scroll-wrapper {
    width: 100%;
    height: 100%;
    padding: 32px 32px 0px 32px;
    overflow-y: auto;
  }
}
.topic-recommend {
  flex: 1;
  overflow: hidden;
}
.scroll-down {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 160px;
  right: calc(50% - 40px);
  background: #ffffff;
  box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.1);
}
.footer {
  flex-shrink: 0;
}
</style>
