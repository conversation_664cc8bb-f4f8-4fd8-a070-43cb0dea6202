<template>
  <div class="message-page">
    <div class="message-header">
      <span class="header-title">消息</span>
      <span class="header-placeholder"></span>
    </div>
    <div class="message-content">
      <div v-if="loading" class="loading-message">加载中...</div>
      <div v-else-if="error" class="error-message">{{ error }}</div>
      <template v-else>
        <div v-if="messages.length === 0" class="empty-message">暂无消息</div>
        <div v-else class="message-list-wrapper">
          <ul class="message-list">
            <li
              v-for="(msg, idx) in messages"
              :key="msg.comment_id || msg.create_time + msg.operator_id"
              class="message-item"
              @click="goToShareDetail(msg.share_id, msg.type)"
            >
              <div class="msg-avatar" :style="{ backgroundColor: getRandomColor(msg.operator_id) }">
                {{ getAvatarLetter(msg.operator_id) }}
              </div>
              <div class="msg-info">
                <div class="msg-header">
                  <span class="msg-operator">{{ msg.operator_id }}</span>
                  <span class="msg-time">{{ msg.create_time }}</span>
                </div>
                <div class="msg-action">
                  <template v-if="msg.type === 'like'">赞了你</template>
                  <template v-else-if="msg.type === 'comment'">评论了你</template>
                </div>
                <div v-if="msg.type === 'comment' && msg.comment_content" class="msg-content-quote">
                  <span class="quote-bar"></span>
                  <span class="quote-text">{{ msg.comment_content }}</span>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </template>
    </div>
    <TabBar default-tab="message" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import TabBar from '@/pages/share/components/TabBar.vue';
import { getNewMessages, markMessagesAsRead } from '@/apis/message';
import { getRandomColor, getAvatarLetter } from '@/utils/avatar';

const router = useRouter();
const goBack = () => {
  router.back();
};

// 跳转到分享详情页
const goToShareDetail = (shareId: string, type: 'like' | 'comment') => {
  if (!shareId) return;
  if (misId && type) {
    try {
      // await markMessagesAsRead({ mis_id: misId, count_type: type });
    } catch (e) {
      // 忽略错误，继续跳转
    }
  }
  void router.push({
    path: `/post/${shareId}`,
  });
};

const loading = ref(true);
interface IMessageItem {
  share_id: string;
  type: 'like' | 'comment';
  comment_id?: string;
  create_time: string;
  operator_id: string;
  comment_content?: string;
}

const messages = ref<IMessageItem[]>([]);
const error = ref('');

const misId = localStorage.getItem('currentMisId') || '';

const fetchMessages = async () => {
  loading.value = true;
  error.value = '';
  try {
    if (!misId) {
      error.value = '未获取到用户ID';
      messages.value = [];
      loading.value = false;
      return;
    }
    const res = await getNewMessages(misId);
    if (res && res.status === 0 && res.data && Array.isArray(res.data.messages)) {
      messages.value = res.data.messages;
    } else {
      error.value = res?.message || '获取消息失败';
      messages.value = [];
    }
  } catch (e: unknown) {
    if (typeof e === 'object' && e !== null && 'message' in e) {
      error.value = (e as { message?: string }).message || '请求出错';
    } else {
      error.value = '请求出错';
    }
    messages.value = [];
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  void fetchMessages();
});
onActivated(() => {
  void fetchMessages();
});
</script>

<style scoped>
.message-list-wrapper {
  width: 100%;
  margin: 0;
  padding: 0 0 0 0;
  box-sizing: border-box;
  background: transparent;
}

.message-header {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  background-color: #f8f8f8;
  z-index: 100;
  box-shadow: none;
  border-bottom: none;
}

.message-page {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 60px;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin: 0 auto !important;
  width: 100%;
  max-width: 520px;
  box-sizing: border-box;
  position: relative;
}

.message-content {
  width: 100%;
  box-sizing: border-box;
  max-height: calc(100vh - 40px - 50px); /* 40px header, 50px底部 */
  overflow-y: auto;
  padding-bottom: 50px;
}

.header-title {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  width: fit-content;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: #222;
  pointer-events: none;
}
.back-btn,
.header-placeholder {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
}
.message-list-wrapper {
  padding: 0 12px;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
}
.message-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.message-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin-top: 16px;
  transition: box-shadow 0.2s;
  padding: 18px 16px 14px 14px;
}
.message-item:hover {
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.13);
}
.msg-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 14px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.07);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
.msg-info {
  flex: 1;
  min-width: 0;
}
.msg-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}
.msg-operator {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-right: 8px;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
.msg-action {
  font-size: 15px;
  color: #373737;
  margin-bottom: 6px;
  margin-top: 3px;
  font-weight: 400;
  line-height: 1.5;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
.msg-content-quote {
  display: flex;
  align-items: flex-start;
  background: #f5f6fa;
  border-radius: 5px;
  margin: 6px 0 0 0;
  padding: 5px 10px;
  border-left: 3px solid #bdbdbd;
}

.quote-bar {
  display: none;
}
.quote-text {
  color: #666;
  font-size: 14px;
  line-height: 1.8;
  word-break: break-all;
  padding-left: 6px;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
.msg-time {
  font-size: 13px;
  color: #999;
  margin-left: 12px;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  white-space: nowrap;
  align-self: center;
  margin-top: 0;
}
.empty-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background-color: #fff;
  border-radius: 12px;
  color: #bbb;
  font-size: 18px;
  margin: 32px 16px 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.loading-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background-color: #fff;
  border-radius: 12px;
  color: #222;
  font-size: 16px;
  margin: 32px 16px 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  letter-spacing: 2px;
  font-weight: 500;
}
.error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background-color: #fff;
  border-radius: 12px;
  color: #222;
  font-size: 18px;
  margin: 32px 16px 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
@media (max-width: 600px) {
  .message-item {
    margin: 14px 6px 0 6px;
    padding: 14px 8px 10px 8px;
  }
  .msg-avatar {
    width: 38px;
    height: 38px;
    font-size: 17px;
    margin-right: 8px;
  }
  .msg-operator {
    font-size: 15px;
  }
  .msg-type {
    font-size: 15px;
  }
  .msg-content {
    font-size: 15px;
  }
}
</style>
