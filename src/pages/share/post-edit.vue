<template>
  <div class="post-edit-page">
    <!-- 顶部导航栏 -->
    <div class="navbar">
      <div class="navbar-left" @click="handleCancel">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M16 4L8 12L16 20" stroke="#222" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <div class="navbar-title">编辑帖子</div>
      <div class="navbar-actions">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="finish-btn" :disabled="loading" @click="handleFinish">完成</button>
      </div>
    </div>

    <!-- 编辑内容区域 -->
    <div class="edit-content">
      <input v-model="title" class="edit-title" maxlength="50" placeholder="请输入标题（最多50字）" />
      <textarea
        v-model="content"
        class="edit-content-text"
        rows="8"
        maxlength="1000"
        placeholder="请输入正文内容（最多1000字）"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { editShare } from '@/apis/share';
import { showSuccessToast, showFailToast } from 'vant';

const router = useRouter();
const route = useRoute();

const loading = ref(false);

// 从路由参数获取初始值
const shareId = route.query.shareId as string;
const misId = route.query.misId as string;
const initTitle = (route.query.title as string) || '';
const initContent = (route.query.content as string) || '';

const title = ref(initTitle);
const content = ref(initContent);

// 取消编辑
function handleCancel() {
  router.back();
}

// 完成编辑
async function handleFinish() {
  if (!title.value.trim() || !content.value.trim()) {
    showFailToast('标题和内容不能为空');
    return;
  }
  loading.value = true;
  try {
    const res = await editShare(shareId, misId, title.value.trim(), content.value.trim());
    if (res && (res.status === 0 || res.code === 0)) {
      showSuccessToast('修改成功');
      setTimeout(() => {
        router.back();
      }, 800);
    } else {
      showFailToast(String(res?.message ?? '修改失败'));
    }
  } catch (e) {
    showFailToast('修改失败');
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.post-edit-page {
  background: #f8f8f8;
  min-height: 100vh;
}
.navbar {
  display: flex;
  align-items: center;
  position: relative;
  background: #f8f8f8;
  height: 48px;
  padding: 20px 15px;
  width: 100%;
  margin: 0 auto;
  max-width: 600px;
  border-bottom: none;
  box-shadow: none;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  font-size: 17px;
  font-weight: bold;
  color: #222;
  pointer-events: none;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  padding-left: 15px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  padding-right: 16px;
  z-index: 2;
}

.post-edit-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.edit-content {
  margin: 0 auto;
  padding: 8px 16px 0 16px;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.navbar-left {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.navbar-title {
  font-size: 17px;
  font-weight: bold;
  color: #222;
}
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.cancel-btn,
.finish-btn {
  background: none;
  border: none;
  color: #222;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 12px;
  border-radius: 6px;
}
.finish-btn {
  color: #ff6b6b;
  font-weight: bold;
}
.edit-content {
  margin-top: 10x;
  padding: 20px 16px 0 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.edit-title {
  width: 100%;
  font-size: 18px;
  font-weight: 500;
  padding: 10px;
  border: none;
  border-radius: 8px;
  background: #fff;
  outline: none;
}
.edit-content-text {
  width: 100%;
  font-size: 16px;
  padding: 10px;
  border: none;
  border-radius: 8px;
  background: #fff;
  outline: none;
  resize: vertical;
}
</style>
