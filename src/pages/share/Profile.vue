<template>
  <div class="profile-container">
    <div class="header">
      <div class="back-button-area">
        <div v-if="!showTabBar" class="back-button" @click="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M15.5 19l-7-7 7-7"
              stroke="#333"
              stroke-width="2"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div class="title">个人主页</div>
      <div class="placeholder"></div>
    </div>

    <div class="user-info-card">
      <div v-if="misId" class="user-avatar" :style="{ backgroundColor: getRandomColor(misId) }">
        {{ getAvatarLetter(misId) }}
      </div>
      <div v-else class="user-avatar avatar-skeleton"></div>
      <div class="user-details">
        <div class="user-name">
          {{ misId }}
          <template v-if="!isSelf">
            <button
              class="follow-btn"
              :class="{ followed: isFollowing }"
              :disabled="followLoading"
              @click="isFollowing ? handleUnfollow() : handleFollow()"
            >
              {{ isFollowing ? '已关注' : '关注' }}
            </button>
          </template>
        </div>
        <div class="user-stats">
          <div class="stat-item">
            <div class="stat-value">{{ userStats.posts }}</div>
            <div class="stat-label">已发帖</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.following }}</div>
            <div class="stat-label">关注</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userStats.followers }}</div>
            <div class="stat-label">粉丝</div>
          </div>
        </div>
      </div>
    </div>

    <!-- tabs上方遮挡层 -->
    <div class="tabs-shield"></div>

    <div class="tabs" style="position: sticky; top: 40px">
      <div class="tab-item" :class="{ active: activeTab === 'shared' }" @click="switchTab('shared')">我发布的</div>
      <div class="tab-item" :class="{ active: activeTab === 'liked' }" @click="switchTab('liked')">已赞</div>
      <div class="tab-item" :class="{ active: activeTab === 'disliked' }" @click="switchTab('disliked')">已踩</div>
    </div>

    <div class="profile-content">
      <div class="content-list">
        <div v-if="posts.length === 0 && !loading" class="empty-state">暂无{{ getTabName() }}的帖子</div>
        <div v-else class="waterfall-container">
          <div class="waterfall-column left-column">
            <div v-for="post in leftPosts" :key="post.id" class="waterfall-item" @click="goToPostDetail(post.id)">
              <div class="item-image">
                <img src="@/assets/img/default-dish.jpeg" alt="分享图片" @load="handleImageLoad" />
              </div>
              <div class="item-content">
                <div class="item-title">{{ post.title }}</div>
                <div class="item-text">{{ post.content }}</div>
                <div class="item-info">
                  <div class="user-info" @click.stop="goToUserProfileById(post.mis_id_keyword)">
                    <div class="avatar" :style="{ backgroundColor: getRandomColor(post.mis_id_keyword) }">
                      {{ post.mis_id_keyword.charAt(0) }}
                    </div>
                    <span class="username" :title="post.mis_id_keyword">{{ post.mis_id_keyword }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="waterfall-column right-column">
            <div v-for="post in rightPosts" :key="post.id" class="waterfall-item" @click="goToPostDetail(post.id)">
              <div class="item-image">
                <img src="@/assets/img/default-dish.jpeg" alt="分享图片" @load="handleImageLoad" />
              </div>
              <div class="item-content">
                <div class="item-title">{{ post.title }}</div>
                <div class="item-text">{{ post.content }}</div>
                <div class="item-info">
                  <div class="user-info" @click.stop="goToUserProfileById(post.mis_id_keyword)">
                    <div class="avatar" :style="{ backgroundColor: getRandomColor(post.mis_id_keyword) }">
                      {{ post.mis_id_keyword.charAt(0) }}
                    </div>
                    <span class="username" :title="post.mis_id_keyword">{{ post.mis_id_keyword }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="loading" class="loading-more">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>

        <!-- 观察元素，用于触发加载更多 -->
        <div ref="observerTarget" class="observer-target"></div>
      </div>
    </div>

    <TabBar v-if="showTabBar" default-tab="profile" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { getRandomColor, getAvatarLetter } from '@/utils/avatar';
import { useRoute, useRouter } from 'vue-router';
import TabBar from '@/pages/share/components/TabBar.vue';
import { RouteName } from '@/constants/route-name';
import {
  getUserSharedShares,
  getUserLikedShares,
  getUserDislikedShares,
  getFollowers,
  getFollowing,
  followUser,
  unfollowUser,
} from '@/apis/userprofile';
import type { IShareItem } from '@/apis/share';

interface IPost extends IShareItem {
  // 扩展IShareItem接口，如果需要额外属性可以在这里添加
}

export default defineComponent({
  name: 'UserProfile',
  components: { TabBar },
  setup() {
    const route = useRoute();
    // fromTab 可能为 string | string[] | undefined
    const showTabBar = computed(() => {
      const { fromTab } = route.query;
      if (Array.isArray(fromTab)) {
        return fromTab.includes('1');
      }
      return String(fromTab) === '1';
    });
    const router = useRouter();
    // 优先从路由参数获取misId，避免API缺参报错
    const misId = ref(route.params.id ? String(route.params.id) : '');
    // 当前登录用户ID，假设存储在localStorage（如有全局store请替换）
    const currentUserId = localStorage.getItem('currentMisId') || '';
    const isSelf = computed(() => currentUserId === misId.value);
    const isFollowing = ref(false);
    const followLoading = ref(false);
    // 将帖子分为左右两列，用于瀑布流布局
    const leftPosts = computed(() => {
      return posts.value.filter((_, index) => index % 2 === 0);
    });
    const rightPosts = computed(() => {
      return posts.value.filter((_, index) => index % 2 === 1);
    });

    // 检查自己是否在对方粉丝列表（即是否已关注对方）
    const checkFollowing = async () => {
      if (isSelf.value) return;
      try {
        const res = await getFollowers({ mis_id: misId.value, offset: 0, batch_size: 100 });
        if (res?.data?.followers?.some((item: { mis_id: string }) => item.mis_id === currentUserId)) {
          isFollowing.value = true;
        } else {
          isFollowing.value = false;
        }
      } catch (e) {
        isFollowing.value = false;
      }
    };

    const handleFollow = async () => {
      if (followLoading.value) return;
      followLoading.value = true;
      // 记录原始状态
      const prevIsFollowing = isFollowing.value;
      const prevFollowers = userStats.value.followers;
      // 立即本地更新
      isFollowing.value = true;
      userStats.value.followers += 1;
      try {
        const res = await followUser({ mis_id: currentUserId, guanzhu_misid: misId.value });
        if (res.status !== 0) {
          // 回滚状态
          isFollowing.value = prevIsFollowing;
          userStats.value.followers = prevFollowers;
          if (window.$toast) {
            window.$toast(res.message || '关注失败');
          }
        }
      } catch (e: unknown) {
        // 回滚状态
        isFollowing.value = prevIsFollowing;
        userStats.value.followers = prevFollowers;
        if (window.$toast) window.$toast(e instanceof Error ? e.message : '关注失败');
      } finally {
        followLoading.value = false;
      }
    };

    const handleUnfollow = async () => {
      if (followLoading.value) return;
      followLoading.value = true;
      // 记录原始状态
      const prevIsFollowing = isFollowing.value;
      const prevFollowers = userStats.value.followers;
      // 立即本地更新
      isFollowing.value = false;
      userStats.value.followers = Math.max(0, userStats.value.followers - 1);
      try {
        const res = await unfollowUser({ mis_id: currentUserId, guanzhu_misid: misId.value });
        if (res.status !== 0) {
          // 回滚状态
          isFollowing.value = prevIsFollowing;
          userStats.value.followers = prevFollowers;
          if (window.$toast) {
            window.$toast(res.message || '取消关注失败');
          }
        }
      } catch (e: unknown) {
        // 回滚状态
        isFollowing.value = prevIsFollowing;
        userStats.value.followers = prevFollowers;
        if (window.$toast) window.$toast(e instanceof Error ? e.message : '取消关注失败');
      } finally {
        followLoading.value = false;
      }
    };

    watch(misId, () => {
      void checkFollowing();
    });
    onMounted(() => {
      void checkFollowing();
    });
    const activeTab = ref('shared'); // 默认展示用户发布的帖子
    const posts = ref<IPost[]>([]);
    const isCurrentUser = ref(false);

    // 分页相关
    const loading = ref(false);
    const offset = ref(0);
    const batchSize = ref(10);
    const hasMore = ref(true);
    const observerTarget = ref<HTMLElement | null>(null);
    let observer: IntersectionObserver | null = null;

    const userStats = ref({
      posts: 0,
      following: 0,
      followers: 0,
    });

    // 切换标签页
    const switchTab = (tab: string) => {
      if (activeTab.value === tab) return;
      activeTab.value = tab;
      // 重置分页数据
      offset.value = 0;
      posts.value = [];
      hasMore.value = true;
      // 获取新标签页的数据
      void fetchData();
    };

    // 获取当前标签名称
    const getTabName = () => {
      switch (activeTab.value) {
        case 'shared':
          return '发布';
        case 'liked':
          return '喜欢';
        case 'disliked':
          return '不喜欢';
        default:
          return '';
      }
    };

    // 监听标签变化
    watch(
      () => activeTab.value,
      () => {
        console.log('标签切换为:', activeTab.value);
      },
    );

    onMounted(() => {
      // 获取路由参数中的用户ID
      const userId = route.params.id as string;

      // 如果没有指定用户ID，则显示当前用户的个人页
      if (!userId) {
        // 从本地存储获取当前用户信息
        const currentUser = localStorage.getItem('currentMisId');
        if (currentUser) {
          misId.value = currentUser;
          isCurrentUser.value = true;
        }
      } else {
        misId.value = userId;
        // 判断是否是当前用户
        const currentUser = localStorage.getItem('currentMisId');
        isCurrentUser.value = currentUser === userId;
      }

      // 获取用户统计数据
      void fetchUserStats();

      // 初始加载数据
      void fetchData();

      // 设置交叉观察器
      setTimeout(() => {
        setupIntersectionObserver();
      }, 500); // 给一些时间确保元素已渲染
    });

    onUnmounted(() => {
      // 清理观察器
      if (observer) {
        observer.disconnect();
        observer = null;
      }
    });

    // 创建观察器
    const setupIntersectionObserver = () => {
      if (!observerTarget.value) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && !loading.value && hasMore.value) {
            console.log('观察器触发加载，当前偏移量:', offset.value);
            void fetchData();
          }
        },
        { threshold: 0.1 },
      );

      observer.observe(observerTarget.value);
      console.log('观察器已设置');
    };

    // 格式化点赞数
    const formatLikeCount = (count: number) => {
      if (count < 1000) return count.toString();
      if (count < 10000) return `${(count / 1000).toFixed(1)}k`;
      return `${(count / 10000).toFixed(1)}w`;
    };

    // 获取用户统计数据
    const fetchUserStats = async () => {
      try {
        // 获取用户粉丝数
        const followersResponse = await getFollowers({
          mis_id: misId.value,
        });
        if (followersResponse.status === 0) {
          userStats.value.followers = followersResponse.data.total;
        }

        // 获取用户关注数
        const followingResponse = await getFollowing({
          mis_id: misId.value,
        });
        if (followingResponse.status === 0) {
          userStats.value.following = followingResponse.data.total;
        }

        // 获取用户发布的帖子数量
        const sharedResponse = await getUserSharedShares({
          mis_id: misId.value,
          offset: 0,
          batch_size: 1,
        });
        if (sharedResponse.status === 0) {
          userStats.value.posts = sharedResponse.data.total;
        }
      } catch (error) {
        console.error('获取用户统计数据失败:', error);
      }
    };

    // 获取数据
    const fetchData = async () => {
      if (loading.value || !hasMore.value) return;
      loading.value = true;

      try {
        let response;
        switch (activeTab.value) {
          case 'shared':
            response = await getUserSharedShares({
              mis_id: misId.value,
              offset: offset.value,
              batch_size: batchSize.value,
            });
            console.log('发布的帖子API响应:', response); // 调试输出

            if (response && response.status === 0 && response.data) {
              // 检查status而不是code
              // 根据实际API结构获取数据
              const shares = response.data.shared_shares || [];
              const total = response.data.total || 0;
              const hasMoreFlag = response.data.has_more || false;

              // 确保每个帖子都有 mis_id 参数
              const processedShares = shares.map((share: IPost) => ({
                ...share,
                mis_id: share.mis_id || misId.value, // 如果帖子没有 mis_id，使用当前用户的 misId
              }));

              console.log('处理后的帖子数据:', processedShares); // 调试输出

              // 判断是否还有更多数据
              hasMore.value = hasMoreFlag;

              if (offset.value === 0) {
                posts.value = processedShares;
              } else {
                posts.value = [...posts.value, ...processedShares];
              }

              // 更新偏移量
              offset.value += shares.length;
              // 更新用户统计数据
              userStats.value.posts = total;
            }
            break;

          case 'liked':
            response = await getUserLikedShares({
              mis_id: misId.value,
              offset: offset.value,
              batch_size: batchSize.value,
            });
            console.log('喜欢的帖子API响应:', response); // 调试输出

            if (response && response.status === 0 && response.data) {
              // 检查status而不是code
              // 根据实际API结构获取数据
              const shares = response.data.liked_shares || [];
              const total = response.data.total || 0;
              const hasMoreFlag = response.data.has_more || false;

              console.log('处理后的喜欢帖子数据:', shares); // 调试输出

              // 判断是否还有更多数据
              hasMore.value = hasMoreFlag;

              if (offset.value === 0) {
                posts.value = shares;
              } else {
                posts.value = [...posts.value, ...shares];
              }

              // 更新偏移量
              offset.value += shares.length;
            }
            break;

          case 'disliked':
            response = await getUserDislikedShares({
              mis_id: misId.value,
              offset: offset.value,
              batch_size: batchSize.value,
            });
            console.log('不喜欢的帖子API响应:', response); // 调试输出

            if (response && response.status === 0 && response.data) {
              // 检查status而不是code
              // 根据实际API结构获取数据
              const shares = response.data.disliked_shares || [];
              const total = response.data.total || 0;
              const hasMoreFlag = response.data.has_more || false;

              console.log('处理后的不喜欢帖子数据:', shares); // 调试输出

              // 判断是否还有更多数据
              hasMore.value = hasMoreFlag;

              if (offset.value === 0) {
                posts.value = shares;
              } else {
                posts.value = [...posts.value, ...shares];
              }

              // 更新偏移量
              offset.value += shares.length;
            }
            break;
          default:
            // 未知tab类型，直接返回
            break;
        }
      } catch (error) {
        console.error(`获取${getTabName()}的帖子失败:`, error);
      } finally {
        loading.value = false;
      }
    };

    // 返回上一页
    const goBack = () => {
      // 获取上一个页面标签，默认返回首页
      const previousTab = localStorage.getItem('previousTab') || 'home';

      if (previousTab === 'home') {
        void router.push({ name: RouteName.SHARE });
      } else if (previousTab === 'recommend') {
        void router.push({ name: RouteName.RECOMMEND });
      } else {
        router.back();
      }
    };

    // 跳转到帖子详情页
    const goToPostDetail = (postId: string) => {
      void router.push({
        name: RouteName.POST_DETAIL,
        params: { id: postId },
      });
    };

    // 跳转到用户个人页面
    const goToUserProfileById = (userId: string) => {
      if (!userId) return;
      void router.push({
        name: RouteName.USER_PROFILE,
        params: { id: userId },
      });
    };

    // 图片加载完成后处理
    const handleImageLoad = (event: Event) => {
      // 图片加载完成后可以执行一些操作，比如调整布局等
    };

    return {
      misId,
      activeTab,
      posts,
      leftPosts,
      rightPosts,
      loading,
      hasMore,
      userStats,
      isFollowing,
      followLoading,
      isSelf,
      observerTarget,
      getRandomColor,
      getAvatarLetter,
      formatLikeCount,
      goToPostDetail,
      goToUserProfileById,
      goBack,
      switchTab,
      getTabName,
      handleImageLoad,
      handleFollow,
      handleUnfollow,
      showTabBar,
    };
  },
});
</script>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  position: relative;
  z-index: 1;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  box-shadow: none;
  border-bottom: none;
}
.back-button-area {
  width: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  position: relative;
  left: 0;
  right: 0;
  margin: 0 auto;
  pointer-events: none;
}
.placeholder {
  width: 40px;
  min-width: 40px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.back-button span {
  margin-left: 4px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.user-info-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 16px;
  margin-left: 16px;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22px;
  font-weight: 800;
  color: white;
  margin-right: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
}

.user-stats {
  display: flex;
  margin-top: 8px;
}

.stat-item {
  margin-right: 24px;
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 16px;
  cursor: pointer;
  transition:
    background-color 0.2s,
    color 0.2s;
}

.tab-item.active {
  color: #ff6b6b;
  font-weight: 600;
  position: relative;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 3px;
  background-color: #ff6b6b;
  border-radius: 3px 3px 0 0;
}

.content-list {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: white;
  border-radius: 12px;
  color: #999;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.waterfall-container {
  display: flex;
  justify-content: space-between;
}

.waterfall-column {
  width: 49%;
}

.waterfall-item {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.waterfall-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.item-image {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  display: block;
  object-fit: cover;
  object-position: center;
}

.item-content {
  padding: 10px;
}

.item-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-text {
  font-size: 15px;
  color: #666;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(15px * 1.4 * 2); /* 字体大小 * 行高 * 行数 */
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}

.username {
  font-size: 14px;
  color: #666;
  font-weight: 400;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #2ec398;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.loading-more span {
  font-size: 14px;
  color: #666;
}

.observer-target {
  height: 20px;
  width: 100%;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.profile-content {
  padding: 16px;
  padding-bottom: 80px;
}

.user-info-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  color: white;
  margin-right: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
}

.user-stats {
  display: flex;
}

.stat-item {
  margin-right: 24px;
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.tabs {
  display: flex;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 16px;
  margin-left: 16px;
  margin-right: 16px;
  position: sticky;
  top: 40px;
  z-index: 99;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 16px;
  cursor: pointer;
  transition:
    background-color 0.2s,
    color 0.2s;
}

.tab-item.active {
  color: #2ec398;
  font-weight: 600;
  position: relative;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 3px;
  background-color: #2ec398;
  border-radius: 3px 3px 0 0;
}

.content-list {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: white;
  border-radius: 12px;
  color: #999;
  font-size: 16px;
}

.post-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.post-item {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.post-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
}

.post-title {
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-stats {
  display: flex;
  padding: 0 8px 8px;
  color: #666;
  font-size: 12px;
}

.post-likes {
  display: flex;
  align-items: center;
}

.post-likes svg {
  margin-right: 4px;
  color: #ff6b6b;
}
.follow-btn {
  margin-left: 12px;
  padding: 4px 16px;
  font-size: 14px;
  border-radius: 16px;
  border: none;
  outline: none;
  background-color: #2ec398;
  color: #fff;
  cursor: pointer;
  transition: background 0.2s;
}
.follow-btn.followed {
  background-color: #e5e5e5;
  color: #888;
  cursor: pointer;
}
.follow-btn:active {
  opacity: 0.85;
}
.follow-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tabbar-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

/* tabs上方遮挡层 */
.tabs-shield {
  position: sticky;
  top: 26px;
  height: 24px;
  background-color: #f8f8f8;
  z-index: 98;
  margin-bottom: -24px;
}
</style>
