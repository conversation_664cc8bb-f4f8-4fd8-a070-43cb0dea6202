<template>
  <div class="search-result-container">
    <!-- 头部搜索区域 -->
    <div>
      <SearchHeader :initial-keyword="keyword" :auto-focus="false" @search="handleSearch" />
    </div>

    <!-- 瀑布流内容区域 -->
    <div ref="shareContainerRef" class="share-container">
      <div v-if="allItems.length === 0 && !loading" class="no-result">
        <p>没有找到相关内容</p>
      </div>
      <div v-else ref="waterfallRef" class="waterfall-container">
        <div class="waterfall-column left-column">
          <div v-for="item in leftItems" :key="item.id" class="waterfall-item" @click="handleItemClick(item)">
            <div class="item-image">
              <img src="@/assets/img/default-dish.jpeg" alt="分享图片" @load="handleImageLoad" />
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.title }}</div>
              <div class="item-text">{{ item.content }}</div>
              <div class="item-info">
                <div class="user-info" @click.stop="goToUserProfileById(item.username)">
                  <div class="avatar" :style="{ backgroundColor: getRandomColor(item.username) }">
                    {{ item.username.charAt(0) }}
                  </div>
                  <span class="username" :title="item.username">{{ item.username }}</span>
                </div>
                <div class="action-buttons">
                  <div class="like-container" @click.stop="toggleLike(item)">
                    <span class="like-icon" :class="{ liked: item.isLiked }"></span>
                    <span class="like-count">{{ formatLikeCount(item.likeCount) }}</span>
                  </div>
                  <div class="dislike-container" @click.stop="toggleDislike(item)">
                    <span class="dislike-icon" :class="{ disliked: item.isDisliked }"></span>
                    <span class="dislike-count">{{ formatLikeCount(item.dislikeCount) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="waterfall-column right-column">
          <div v-for="item in rightItems" :key="item.id" class="waterfall-item" @click="handleItemClick(item)">
            <div class="item-image">
              <img src="@/assets/img/default-dish.jpeg" alt="分享图片" @load="handleImageLoad" />
            </div>
            <div class="item-content">
              <div class="item-title">{{ item.title }}</div>
              <div class="item-text">{{ item.content }}</div>
              <div class="item-info">
                <div class="user-info" @click.stop="goToUserProfileById(item.username)">
                  <div class="avatar" :style="{ backgroundColor: getRandomColor(item.username) }">
                    {{ item.username.charAt(0) }}
                  </div>
                  <span class="username" :title="item.username">{{ item.username }}</span>
                </div>
                <div class="action-buttons">
                  <div class="like-container" @click.stop="toggleLike(item)">
                    <span class="like-icon" :class="{ liked: item.isLiked }"></span>
                    <span class="like-count">{{ formatLikeCount(item.likeCount) }}</span>
                  </div>
                  <div class="dislike-container" @click.stop="toggleDislike(item)">
                    <span class="dislike-icon" :class="{ disliked: item.isDisliked }"></span>
                    <span class="dislike-count">{{ formatLikeCount(item.dislikeCount) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="loading" class="loading-more">
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      </div>

      <!-- 观察元素，用于触发加载更多 -->
      <div ref="observerTarget" class="observer-target"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { RouteName } from '@/constants/route-name';
import {
  searchShare,
  IShareItem as IShareItemApi,
  likeShare,
  unlikeShare,
  dislikeShare,
  undislikeShare,
} from '@/apis/share';
import { getUserInfo } from '@/apis/common';
import SearchHeader from './components/SearchHeader.vue';

// 页面展示数据结构
interface IShareItem {
  id: string;
  title: string;
  content: string;
  username: string;
  time: string;
  likeCount: number;
  isLiked: boolean;
  dislikeCount: number;
  isDisliked: boolean;
}

const router = useRouter();
const route = useRoute();
const keyword = ref('');
const searchInputRef = ref<HTMLInputElement | null>(null);
const loading = ref(false);
const allItems = ref<IShareItem[]>([]);
const imageRatios = reactive<Record<string, number>>({});
const currentPage = ref(0);
const pageSize = ref(10);
const hasMore = ref(true);
const shareContainerRef = ref<HTMLElement | null>(null);
const waterfallRef = ref<HTMLElement | null>(null);
const observerTarget = ref<HTMLElement | null>(null);

// 获取搜索结果数据
const fetchData = async () => {
  if (loading.value || !hasMore.value || !keyword.value.trim()) {
    return;
  }

  loading.value = true;

  try {
    const response = await searchShare(keyword.value, {
      offset: currentPage.value * pageSize.value,
      batch_size: pageSize.value,
    });

    if (response.code === 200) {
      const { shares, has_more: hasMoreData } = response.data;

      // 转换数据格式
      const newItems: IShareItem[] = shares.map((item: IShareItemApi) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        username: item.mis_id_keyword,
        time: item.time,
        likeCount: item.like_count,
        isLiked: !!item.is_liked,
        dislikeCount: item.dislike_count || 0,
        isDisliked: !!item.is_disliked,
      }));

      // 添加到现有数据中
      allItems.value = [...allItems.value, ...newItems];

      // 更新分页信息
      currentPage.value += 1;
      hasMore.value = hasMoreData;
    } else {
      console.error('搜索失败:', response);
    }
  } catch (error) {
    console.error('搜索请求出错:', error);
  } finally {
    loading.value = false;
  }
};

// 监听路由参数变化，更新搜索关键词
watch(
  () => route.query.keyword,
  (newKeyword) => {
    if (newKeyword) {
      keyword.value = newKeyword as string;
      // 重置搜索结果
      allItems.value = [];
      currentPage.value = 0;
      hasMore.value = true;
      // 重新搜索
      void fetchData();
    }
  },
  { immediate: true },
);

// 分为左右两列
const leftItems = computed(() => {
  return allItems.value.filter((_, index) => index % 2 === 0);
});

const rightItems = computed(() => {
  return allItems.value.filter((_, index) => index % 2 === 1);
});

// 处理图片加载完成事件
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  const url = img.src;
  const ratio = img.naturalWidth / img.naturalHeight;
  imageRatios[url] = ratio;
};

// 格式化点赞数
const formatLikeCount = (count: number): string => {
  if (count >= 10000) {
    // 超过1万，使用万作为单位，保留一位小数
    const wan = Math.floor(count / 1000) / 10;
    // 如果小数点后是0，则去掉小数点
    return wan % 1 === 0 ? `${Math.floor(wan)}万` : `${wan}万`;
  }
  // 千及以下用4位数展示
  return count.toString();
};

// 切换点赞状态
const toggleLike = async (item: IShareItem) => {
  try {
    // 先立即更新UI状态，提供即时反馈
    const originalLikeCount = item.likeCount;
    const originalIsLiked = item.isLiked;

    // 获取用户ID - 从getUserInfo接口获取login字段
    const userInfo = await getUserInfo();
    const misId = userInfo.login || 'liulingfeng05';

    // 先切换状态
    item.isLiked = !item.isLiked;

    if (originalIsLiked) {
      // 如果之前已经点赞，则取消点赞
      item.likeCount = Math.max(0, item.likeCount - 1); // 确保不会出现负数
      // 发送API请求
      const response = await unlikeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已取消点赞）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isLiked = originalIsLiked;
        item.likeCount = originalLikeCount;
        console.error('取消点赞失败:', response);
      }
    } else {
      // 如果之前未点赞，则添加点赞
      item.likeCount += 1;
      // 发送API请求
      const response = await likeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已点赞）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isLiked = originalIsLiked;
        item.likeCount = originalLikeCount;
        console.error('点赞失败:', response);
      }

      // 如果同时点了踩，则先取消点踩再点赞
      if (item.isDisliked) {
        // 先更新UI状态
        const originalDislikeCount = item.dislikeCount;
        item.isDisliked = false;
        item.dislikeCount = Math.max(0, item.dislikeCount - 1);

        // 发送API请求取消点踩
        try {
          await undislikeShare(item.id, misId);
        } catch (error) {
          console.error('取消点踩失败:', error);
          // 如果取消点踩失败，不恢复状态，因为点赞已经成功
        }
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
  }
};

// 切换点踩状态
const toggleDislike = async (item: IShareItem) => {
  try {
    // 先立即更新UI状态，提供即时反馈
    const originalDislikeCount = item.dislikeCount;
    const originalIsDisliked = item.isDisliked;

    // 获取用户ID
    const userInfo = await getUserInfo();
    const misId = userInfo.login || 'liulingfeng05';

    // 先切换状态
    item.isDisliked = !item.isDisliked;

    if (originalIsDisliked) {
      // 如果之前已经点踩，则取消点踩
      item.dislikeCount = Math.max(0, item.dislikeCount - 1); // 确保不会出现负数
      // 发送API请求
      const response = await undislikeShare(item.id, misId);
      // 检查响应状态
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isDisliked = originalIsDisliked;
        item.dislikeCount = originalDislikeCount;
        console.error('取消点踩失败:', response);
      }
    } else {
      // 如果之前未点踩，则添加点踩
      item.dislikeCount += 1;
      // 发送API请求
      const response = await dislikeShare(item.id, misId);
      // 检查响应状态
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isDisliked = originalIsDisliked;
        item.dislikeCount = originalDislikeCount;
        console.error('点踩失败:', response);
      }

      // 如果同时点了赞，则先取消点赞再点踩
      if (item.isLiked) {
        // 先更新UI状态
        const originalLikeCount = item.likeCount;
        item.isLiked = false;
        item.likeCount = Math.max(0, item.likeCount - 1);

        // 发送API请求取消点赞
        try {
          await unlikeShare(item.id, misId);
        } catch (error) {
          console.error('取消点赞失败:', error);
          // 如果取消点赞失败，不恢复状态，因为点踩已经成功
        }
      }
    }
  } catch (error) {
    console.error('点踩操作失败:', error);
  }
};

// 根据用户名生成随机颜色
const getRandomColor = (username: string) => {
  // 使用用户名作为种子生成一致的颜色
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    // eslint-disable-next-line no-bitwise
    hash = username.charCodeAt(i) + ((hash << 5) - hash);
  }

  // 选择一些清晰的颜色
  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#FFA45C',
    '#65D6AD',
    '#FF9F80',
    '#9896F1',
    '#D65DB1',
    '#6A8CAF',
    '#F9C449',
  ];

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

// 获取图片样式
const getImageStyle = (imageUrl: string) => {
  // 如果图片还没加载完，返回默认样式
  if (!imageRatios[imageUrl]) {
    return {};
  }

  const ratio = imageRatios[imageUrl];

  // 横图，宽高比大于4:3的裁剪为4:3
  if (ratio > 1) {
    if (ratio > 4 / 3) {
      return { aspectRatio: '4/3' };
    }
  }
  // 竖图，宽高比小于3:4的裁剪为3:4
  else if (ratio < 3 / 4) {
    return { aspectRatio: '3/4' };
  }

  // 如果在允许范围内，保持原始比例
  return { height: 'auto' };
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 处理搜索
const handleSearch = (newKeyword: string) => {
  // 更新关键词
  keyword.value = newKeyword;

  // 重置搜索结果
  allItems.value = [];
  currentPage.value = 0;
  hasMore.value = true;

  // 更新URL参数但不重新加载页面
  void router.replace({
    query: {
      ...route.query,
      keyword: newKeyword,
    },
  });

  // 搜索数据
  void fetchData();
};

// 使用IntersectionObserver监听元素是否可见
let observer: IntersectionObserver | null = null;

// 创建观察器
const setupIntersectionObserver = () => {
  if (!observerTarget.value) return;

  observer = new IntersectionObserver(
    (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && !loading.value && hasMore.value) {
        void fetchData();
      }
    },
    {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    },
  );

  observer.observe(observerTarget.value);
};

// 点击卡片
const handleItemClick = (item: IShareItem) => {
  void router.push({
    name: RouteName.POST_DETAIL,
    params: {
      id: item.id,
    },
  });
};

// 跳转到指定用户的个人页面
const goToUserProfileById = (userId: string) => {
  void router.push({
    name: RouteName.USER_PROFILE,
    params: {
      id: userId,
    },
  });
};

onMounted(() => {
  // 初始加载数据
  if (keyword.value.trim()) {
    void fetchData();
  }

  // 设置观察器
  setupIntersectionObserver();

  // 自动聚焦输入框
  if (searchInputRef.value) {
    searchInputRef.value.focus();
  }
});

onUnmounted(() => {
  // 清理观察器
  if (observer) {
    observer.disconnect();
  }
});
</script>

<style lang="scss" scoped>
.search-result-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整体出现滚动条 */
  background-image: url(@/assets/img/page-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  /* 移除淡入动画 */

  .search-header {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    background-color: transparent;
    box-shadow: transparent;

    .back-icon {
      cursor: pointer;
      padding: 8px;
      margin-right: 10px;
      border-radius: 50%;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .search-input-container {
      flex: 1;
      margin-right: 15px;

      .search-input {
        width: 100%;
        height: 45px;
        border: 1px solid #ddd;
        border-radius: 22px;
        padding: 0 20px;
        font-size: 25px;
        outline: none;

        &:focus {
          border-color: #4a90e2;
          box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }
      }
    }

    .search-button {
      padding: 10px 20px;
      background-color: #4a90e2;
      color: white;
      border-radius: 22px;
      font-size: 25px;
      cursor: pointer;

      &:hover {
        background-color: #3a80d2;
      }
    }
  }

  .share-container {
    padding: 20px 25px 20px;
    flex: 1;
    overflow: auto;
    background-color: transparent;
    margin-bottom: 10px;
    padding-bottom: 110px; /* 为底部导航栏留出空间 */
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .no-result {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      p {
        font-size: 25px;
        color: #666;
      }
    }

    .waterfall-container {
      display: flex;
      justify-content: space-between;

      .waterfall-column {
        width: 49%;

        .waterfall-item {
          margin-bottom: 15px;
          background-color: #fff;
          border-radius: 13px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: box-shadow 0.3s ease;

          .item-image {
            width: 100%;
            img {
              width: 100%;
              height: auto;
              display: block;
              object-fit: cover;
            }
          }

          .item-content {
            padding: 20px;

            .item-title {
              font-size: 30px;
              font-weight: 500;
              margin-bottom: 10px;
              word-break: break-word;
              line-height: 1.3;
            }

            .item-text {
              font-size: 24px;
              color: #666;
              margin-bottom: 15px;
              word-break: break-word;
              line-height: 1.5;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .item-info {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .user-info {
                display: flex;
                align-items: center;
                flex: 1;

                .avatar {
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 22px;
                  font-weight: 500;
                  margin-right: 10px;
                }

                .username {
                  font-size: 24px;
                  color: #666;
                  font-weight: 350;
                  max-width: 140px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: inline-block;
                }
              }

              .action-buttons {
                display: flex;
                align-items: center;
                gap: 3px;
              }

              .like-container,
              .dislike-container {
                display: flex;
                align-items: center;
                cursor: pointer;
                padding: 5px;
                min-width: 30px;
                justify-content: flex-end;

                &:hover .like-icon,
                &:hover .dislike-icon {
                  transform: scale(1.05);
                }
                &:active .like-icon,
                &:active .dislike-icon {
                  transform: scale(0.8);
                  transition: transform 0.1s ease;
                }

                .like-icon {
                  width: 26px;
                  height: 26px;
                  display: inline-block;
                  background-color: #999;
                  -webkit-mask: url('~@/assets/img/like.svg') no-repeat center/contain;
                  mask: url('~@/assets/img/like.svg') no-repeat center/contain;
                  transition: all 0.2s;
                  vertical-align: middle;
                  margin-right: 4px;
                }
                .like-icon.liked {
                  background-color: #ff2222;
                }
                .dislike-icon {
                  width: 26px;
                  height: 26px;
                  display: inline-block;
                  background-color: #999;
                  -webkit-mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
                  mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
                  transition: all 0.2s;
                  vertical-align: middle;
                  margin-right: 4px;
                }
                .dislike-icon.disliked {
                  background-color: #303030;
                }
                .like-count {
                  font-size: 18px;
                  color: #666;
                  min-width: 16px;
                  text-align: right;
                  transition: all 0.2s ease;
                  font-weight: 600;
                  margin-left: 0;
                }
                .like-icon.liked + .like-count {
                  color: #ff2222;
                }

                .dislike-count {
                  font-size: 18px;
                  color: #666;
                  min-width: 16px;
                  text-align: right;
                  transition: all 0.2s ease;
                  font-weight: 600;
                  margin-left: 0;
                }
                .dislike-icon.disliked + .dislike-count {
                  color: #303030;
                }
              }
            }
          }

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    .loading-more {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;
      color: #666;
      font-size: 25px;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #4a90e2;
        animation: spin 1s linear infinite;
        margin-right: 10px;
      }
    }

    .observer-target {
      height: 20px;
      width: 100%;
    }
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
