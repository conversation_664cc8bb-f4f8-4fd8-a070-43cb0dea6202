<template>
  <div class="post-detail-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M15.5 19l-7-7 7-7"
            stroke="#333"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>

      <!-- 用户信息 -->
      <div class="user-info" style="cursor: pointer; display: flex; align-items: center; gap: 12px">
        <div
          class="user-info-row"
          style="display: flex; align-items: center"
          @click="goToUserProfileById(postData?.mis_id_keyword || '')"
        >
          <div class="avatar" :style="{ backgroundColor: postData?.avatarColor }">
            {{ getAvatarLetter(postData?.mis_id_keyword || '') }}
          </div>
          <span class="username" :title="postData?.mis_id_keyword">{{ postData?.mis_id_keyword }}</span>
          <button
            v-if="!isSelf"
            class="follow-btn"
            :class="{ followed: isFollowing }"
            :disabled="followLoading"
            style="margin-left: 12px"
            @click.stop="isFollowing ? handleUnfollow() : handleFollow()"
          >
            {{ isFollowing ? '已关注' : '关注' }}
          </button>
        </div>
      </div>

      <!-- 操作菜单按钮（三个点） -->
      <div class="post-actions">
        <button ref="actionBtnRef" class="action-btn no-style" @click.stop="showActionSheet = true">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="5" cy="12" r="2" fill="#222" />
            <circle cx="12" cy="12" r="2" fill="#222" />
            <circle cx="19" cy="12" r="2" fill="#222" />
          </svg>
        </button>
        <div v-if="showActionSheet" class="action-sheet-mask" @click.self="showActionSheet = false">
          <div
            class="action-sheet-popup"
            :style="{ position: 'fixed', top: popupPosition.top + 'px', left: popupPosition.left + 'px' }"
          >
            <div
              v-for="item in actionOptions"
              :key="item.key"
              class="action-sheet-item"
              :class="{ danger: item.key === 'delete' }"
              @click="handleActionSelect(item)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="placeholder"></div>
    </div>

    <!-- 帖子内容 -->
    <div v-if="postData" class="post-content">
      <!-- 图片展示 -->
      <div class="post-image">
        <!-- 图片编号指示器 -->
        <div v-if="postData.food_img_urls && postData.food_img_urls.length > 1" class="image-indicator">
          {{ currentIndex + 1 }}/{{ postData.food_img_urls.length }}
        </div>
        <div
          ref="imageContainer"
          class="image-swiper"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseUp"
        >
          <div class="swiper-wrapper" :style="{ transform: `translateX(${-currentIndex * 100}%)` }">
            <div v-for="(imgUrl, index) in postData.food_img_urls || []" :key="index" class="swiper-slide">
              <div class="slide-content">
                <img
                  :src="imgUrl || defaultImage"
                  alt="帖子图片"
                  class="swipe-img"
                  draggable="false"
                  @click.stop="directShowFullImage(imgUrl || defaultImage)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单详情 -->
      <div v-if="postData.order_detail && postData.order_detail[currentIndex]" class="order-detail-container">
        <div class="order-detail">
          <a href="javascript:void(0)" class="link-button">
            <div class="shop-icon">
              <img :src="currentImageUrl || defaultImage" alt="商品图标" />
            </div>
            <div class="order-info">
              <div class="order-name">{{ postData.order_detail[currentIndex] }}</div>
              <div v-if="postData.money_detail && postData.money_detail[currentIndex]" class="order-price">
                ¥{{ postData.money_detail[currentIndex] }}
              </div>
            </div>
          </a>
        </div>
      </div>

      <!-- 标题 -->
      <div class="post-title">
        {{ postData.title }}
      </div>

      <!-- 正文内容 -->
      <div class="post-body">
        {{ postData.content }}
      </div>

      <!-- 店铺名称 -->
      <div v-if="postData.shop_name" class="shop-name">
        <div class="location-icon">
          <svg width="15" height="15" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
              fill="#666666"
            />
          </svg>
        </div>
        <span>{{ postData.shop_name }}</span>
      </div>

      <!-- 评论区组件 -->
      <CommentSection
        :comments="processedComments"
        :comment-count="postData.comment_count || 0"
        :author-id="postData.mis_id_keyword"
        @reply="handleReply"
        @delete-comment="handleDeleteComment"
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="!postData" class="loading-state">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>

    <!-- 底部交互区域组件 -->
    <InteractionBar
      v-if="postData"
      :reply-to="replyToUser"
      :reply-to-parent-id="replyToParentId"
      :like-count="postData.like_count || 0"
      :dislike-count="postData.dislike_count || 0"
      :comment-count="postData.comment_count || 0"
      :is-liked="isLiked"
      :is-disliked="isDisliked"
      @submit-comment="handleCommentSubmit"
      @cancel-reply="cancelReply"
      @toggle-like="toggleLike"
      @toggle-dislike="toggleDislike"
    />

    <!-- 全屏查看图片 -->
    <teleport to="body">
      <div v-if="isFullImageVisible" class="fullscreen-image" @click="hideFullImage">
        <div class="fullscreen-image-container">
          <img :src="fullImageUrl" alt="全屏图片" class="zoomed-image" />
          <div class="close-button" @click.stop="hideFullImage">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M18 6L6 18M6 6l12 12"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </teleport>
  </div>
  <!-- 删除确认弹窗 -->
  <teleport to="body">
    <div v-if="showDeleteConfirm" class="modal-mask" @click.self="showDeleteConfirm = false">
      <div class="modal-dialog">
        <div class="modal-title">确认删除该帖子？</div>
        <div class="modal-actions">
          <button class="modal-btn cancel" @click="showDeleteConfirm = false">取消</button>
          <button class="modal-btn confirm" :disabled="deleteLoading" @click="handleDeletePost">
            <span v-if="deleteLoading">删除中...</span>
            <span v-else>确认删除</span>
          </button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import {
  deleteShare,
  getShareDetail,
  submitComment,
  storeComment,
  likeShare,
  unlikeShare,
  dislikeShare,
  undislikeShare,
  deleteComment,
  deleteComments,
  IShareItem,
  ICommentItem,
} from '@/apis/share';
import { RouteName } from '@/constants/route-name';
import { getUserInfo } from '@/apis/common';
import { useRoute, useRouter } from 'vue-router';

import { getFollowers, followUser, unfollowUser } from '@/apis/userprofile';
import { showSuccessToast, showFailToast } from 'vant';
import defaultDish from '@/assets/img/default-dish.jpeg';
import { getRandomColor, getAvatarLetter } from '@/utils/avatar';
import TabBar from './components/TabBar.vue';
import CommentSection from './components/CommentSection.vue';
import InteractionBar from './components/InteractionBar.vue';

// 操作菜单相关
const showActionSheet = ref(false);
const actionBtnRef = ref<HTMLElement | null>(null);
const popupPosition = ref<{ top: number; left: number }>({ top: 0, left: 0 });

watch(showActionSheet, async (val) => {
  if (val) {
    await nextTick();
    if (actionBtnRef.value) {
      const rect = actionBtnRef.value.getBoundingClientRect();
      // 默认left为按钮左侧
      // 向左偏移32px
      let left = rect.left - 32;
      const top = rect.bottom + 6;
      // 获取浮窗宽度和窗口宽度，防止溢出
      const popupEl = document.querySelector('.action-sheet-popup') as HTMLElement;
      if (popupEl) {
        const popupWidth = popupEl.offsetWidth || 160;
        const winWidth = window.innerWidth;
        if (left + popupWidth > winWidth - 8) {
          // 右侧溢出，向左偏移
          left = winWidth - popupWidth - 8;
        }
        if (left < 8) left = 8; // 最左不小于8px
      }
      popupPosition.value = {
        top,
        left,
      };
    }
  }
});

// 操作菜单（只用isSelf判断是否本人）
const actionOptions = computed(() => {
  const arr = [];
  arr.push({ name: '分享帖子', key: 'share' });
  if (isSelf.value) {
    arr.push({ name: '编辑帖子', key: 'edit' });
    arr.push({ name: '删除帖子', key: 'delete', color: 'red' });
  }
  return arr;
});

const showDeleteConfirm = ref(false);
const deleteLoading = ref(false);

function handleActionSelect(action: { name: string; key: string }) {
  showActionSheet.value = false;
  if (action.key === 'edit') {
    // 跳转到编辑页面，携带必要参数
    if (postData.value) {
      void router.push({
        name: 'PostEdit',
        query: {
          shareId: String(postData.value.id),
          misId: postData.value.mis_id_keyword,
          title: postData.value.title,
          content: postData.value.content,
        },
      });
    }
  } else if (action.key === 'delete') {
    showDeleteConfirm.value = true;
  } else if (action.key === 'share') {
    // 分享逻辑：复制当前url
    const url = window.location.href;
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(
        () => {
          showSuccessToast('链接已复制，可粘贴分享');
        },
        () => {
          showFailToast('复制失败，请手动复制');
        },
      );
    } else {
      // 兼容不支持clipboard的浏览器
      try {
        const input = document.createElement('input');
        input.value = url;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        showSuccessToast('链接已复制，可粘贴分享');
      } catch {
        showFailToast('复制失败，请手动复制');
      }
    }
  }
}

async function handleDeletePost() {
  console.log('handleDeletePost called', postData.value, currentUserId);
  if (!postData.value || !currentUserId) {
    console.warn('删除前置条件不满足', postData.value, currentUserId);
    return;
  }
  deleteLoading.value = true;
  try {
    const res = await deleteShare(String(postData.value.id), currentUserId);
    if (res && (res.status === 0 || res.code === 0)) {
      showSuccessToast('删除成功');
      showDeleteConfirm.value = false;
      // 跳转回广场页或上一页
      setTimeout(() => {
        if (window.history.length > 1) {
          void router.back();
        } else {
          void router.replace({ name: RouteName.SHARE });
        }
      }, 800);
    } else {
      showFailToast(res?.message || '删除失败');
    }
  } catch (e) {
    showFailToast('删除失败');
  } finally {
    deleteLoading.value = false;
  }
}

const router = useRouter();
const route = useRoute();
const defaultImage = defaultDish; // 默认图片

// 当前索引
const currentIndex = ref(0);

// 图片容器引用
const imageContainer = ref<HTMLElement | null>(null);

// 触控相关变量
const startX = ref(0);
const currentX = ref(0);
const containerWidth = ref(0);
const isDragging = ref(false);
const dragDistance = ref(0); // 记录拖动距离
const isDraggingMode = ref(false); // 是否处于拖动模式
const clickedOnImage = ref(false); // 记录是否点击在图片上

// 当前滑动位移
const slideOffset = ref(0);

// 跳转到个人页面
const goToUserProfileById = (userId: string) => {
  console.log('跳转到指定用户页面', userId);
  // 跳转到指定用户的个人页面
  void router.push({
    name: RouteName.USER_PROFILE,
    params: { id: userId },
  });
};

// 全屏查看图片相关
const isFullImageVisible = ref(false);
const fullImageUrl = ref('');

// 当前图片URL
const currentImageUrl = computed(() => {
  if (!postData.value || !postData.value.food_img_urls || postData.value.food_img_urls.length === 0) {
    return null;
  }
  return postData.value.food_img_urls[currentIndex.value];
});

// 是否有上一张图片
const hasPrevImage = computed(() => {
  return currentIndex.value > 0;
});

// 是否有下一张图片
const hasNextImage = computed(() => {
  if (!postData.value || !postData.value.food_img_urls) return false;
  return currentIndex.value < postData.value.food_img_urls.length - 1;
});

// 当前显示的订单详情和金额
const currentOrderDetail = computed(() => {
  if (!postData.value || !postData.value.order_detail || postData.value.order_detail.length === 0) {
    return null;
  }
  return postData.value.order_detail[currentIndex.value] || null;
});

const currentMoneyDetail = computed(() => {
  if (
    !postData.value ||
    !postData.value.money_detail ||
    typeof postData.value.money_detail === 'string' ||
    postData.value.money_detail.length === 0
  ) {
    return null;
  }
  return postData.value.money_detail[currentIndex.value] || null;
});

// 切换到上一张图片
const prevImage = () => {
  if (hasPrevImage.value) {
    currentIndex.value--;
  }
};

// 切换到下一张图片
const nextImage = () => {
  if (hasNextImage.value) {
    currentIndex.value++;
  }
};

// 初始化容器宽度
const initContainerWidth = () => {
  if (imageContainer.value) {
    containerWidth.value = imageContainer.value.offsetWidth;
  }
};

// 处理触摸开始事件
const handleTouchStart = (e: TouchEvent) => {
  // 记录点击的是否是图片元素，用于后续判断
  clickedOnImage.value = e.target instanceof HTMLImageElement;

  startX.value = e.touches[0].clientX;
  currentX.value = e.touches[0].clientX; // 初始化当前位置
  startTime.value = Date.now(); // 记录开始时间
  isDragging.value = true;
  dragDistance.value = 0; // 重置拖动距离
};

// 处理触摸移动事件
const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return;
  e.preventDefault();
  currentX.value = e.touches[0].clientX;
  dragDistance.value = currentX.value - startX.value; // 记录拖动距离

  // 如果拖动距离超过阈值，设置为拖动模式
  if (Math.abs(dragDistance.value) > 5) {
    isDraggingMode.value = true;
  }
};

// 处理触摸结束事件
const handleTouchEnd = () => {
  handleSwipeEnd();
};

// 处理鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  // 记录点击的是否是图片元素，用于后续判断
  clickedOnImage.value = e.target instanceof HTMLImageElement;

  startX.value = e.clientX;
  currentX.value = e.clientX; // 初始化当前位置
  startTime.value = Date.now(); // 记录开始时间
  isDragging.value = true;
  dragDistance.value = 0; // 重置拖动距离
  e.preventDefault(); // 防止拖拽图片

  // 改变鼠标样式为抓取中
  if (imageContainer.value) {
    imageContainer.value.style.cursor = 'grabbing';
  }
};

// 处理鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return;
  e.preventDefault();
  currentX.value = e.clientX;
  dragDistance.value = currentX.value - startX.value; // 记录拖动距离

  // 如果拖动距离超过阈值，设置为拖动模式
  if (Math.abs(dragDistance.value) > 1.5) {
    isDraggingMode.value = true;
  }
};

// 处理鼠标松开事件
const handleMouseUp = () => {
  handleSwipeEnd();

  // 恢复默认鼠标样式
  if (imageContainer.value) {
    imageContainer.value.style.cursor = 'default';
  }
};

// 记录开始时间，用于区分点击和拖动
const startTime = ref(0);

// 处理滑动结束逻辑（触摸和鼠标共用）
const handleSwipeEnd = () => {
  if (!isDragging.value) return;

  const diff = currentX.value - startX.value;
  const timeDiff = Date.now() - startTime.value;
  const threshold = containerWidth.value * 0.05; // 降低阈值，只需滑动5%宽度即可触发切换
  const isClick = Math.abs(diff) < 10 && timeDiff < 300; // 移动距离小于10px且时间小于300ms视为点击

  // 如果是点击图片且移动距离很小，则视为点击事件
  if (clickedOnImage.value && isClick) {
    // 不执行滑动操作，让点击事件处理
  }
  // 处理滑动事件
  else if (Math.abs(diff) > threshold) {
    // 添加订单卡片缩小效果
    const orderCard = document.querySelector('.order-detail-container') as HTMLElement;
    if (orderCard) {
      // 先缩小
      orderCard.style.transition = 'transform 0.2s ease';
      orderCard.style.transform = 'scale(0.95)';

      // 然后切换图片
      setTimeout(() => {
        if (diff > 0 && currentIndex.value > 0) {
          // 向右滑动，显示上一张
          currentIndex.value--;
        } else if (diff < 0 && hasNextImage.value) {
          // 向左滑动，显示下一张
          currentIndex.value++;
        }

        // 恢复原始大小
        setTimeout(() => {
          orderCard.style.transform = 'scale(1)';
        }, 50);
      }, 150);
    } else {
      // 如果没有找到订单卡片，直接切换图片
      const updateIndex = () => {
        if (diff > 0 && currentIndex.value > 0) {
          currentIndex.value--;
        } else if (diff < 0 && hasNextImage.value) {
          currentIndex.value++;
        }
      };
      updateIndex();
    }
  }

  isDragging.value = false;
  dragDistance.value = 0; // 重置拖动距离
  clickedOnImage.value = false; // 重置点击状态

  // 重置拖动模式状态
  setTimeout(() => {
    isDraggingMode.value = false;
  }, 100); // 等待短暂时间再重置，避免与点击事件冲突
};

// 显示全屏图片 - 从滑动事件中判断点击
const showFullImage = (imgUrl: string) => {
  // 只有在被识别为点击事件时才显示全屏图片
  const diff = Math.abs(currentX.value - startX.value);
  const timeDiff = Date.now() - startTime.value;

  if (diff < 10 && timeDiff < 300) {
    // 移动距离小于10px且时间小于300ms视为点击
    directShowFullImage(imgUrl);
  }
};

// 直接显示全屏图片 - 需要判断是否在拖拽中
const directShowFullImage = (imgUrl: string) => {
  // 如果当前正在拖拽且拖动距离超过阈值，不显示全屏图片
  if (isDraggingMode.value || (isDragging.value && Math.abs(dragDistance.value) > 10)) {
    return;
  }

  // 否则显示全屏图片
  fullImageUrl.value = imgUrl;
  isFullImageVisible.value = true;
  document.body.style.overflow = 'hidden'; // 防止背景滚动
};

// 隐藏全屏图片
const hideFullImage = () => {
  isFullImageVisible.value = false;
  document.body.style.overflow = ''; // 恢复背景滚动
};

// 帖子数据接口，使用IShareItem接口并添加UI所需的额外字段
interface IPostDetail extends Partial<IShareItem> {
  avatar: string;
  username: string;
  imageUrl: string;
  avatarColor: string;
}

const postData = ref<IPostDetail | null>(null);

// 点赞和点踩状态
const isLiked = ref(false);
const isDisliked = ref(false);

// 评论输入框
const commentText = ref('');

// 当前用户ID（localStorage实际key为currentMisId）
const currentUserId = localStorage.getItem('currentMisId') || '';
// 被查看用户ID（即发帖人）
const targetMisId = computed(() => postData.value?.mis_id_keyword || '');
// 是否本人
const isSelf = computed(() => currentUserId === targetMisId.value);
// 是否已关注
const isFollowing = ref(false);
// 关注按钮loading
const followLoading = ref(false);

// 检查是否已关注
const checkFollowing = async () => {
  if (isSelf.value || !targetMisId.value) return;
  try {
    const res = await getFollowers({ mis_id: targetMisId.value, offset: 0, batch_size: 100 });
    isFollowing.value = res?.data?.followers?.some((item: { mis_id: string }) => item.mis_id === currentUserId);
  } catch (e) {
    isFollowing.value = false;
  }
};

// 关注
const handleFollow = async () => {
  if (followLoading.value || !targetMisId.value) return;
  followLoading.value = true;
  try {
    const res = await followUser({ mis_id: currentUserId, guanzhu_misid: targetMisId.value });
    isFollowing.value = res.status === 0;
  } catch (e) {
    isFollowing.value = false;
  } finally {
    followLoading.value = false;
  }
};
// 取消关注
const handleUnfollow = async () => {
  if (followLoading.value || !targetMisId.value) return;
  followLoading.value = true;
  try {
    const res = await unfollowUser({ mis_id: currentUserId, guanzhu_misid: targetMisId.value });
    isFollowing.value = !(res.status === 0);
  } catch (e) {
    isFollowing.value = true;
  } finally {
    followLoading.value = false;
  }
};

watch(
  () => targetMisId.value,
  () => {
    void checkFollowing();
  },
);
onMounted(() => {
  void checkFollowing();
});

// 当前用户头像信息
const userAvatar = ref('我');
const userAvatarColor = ref('#4ECDC4');

// 回复功能相关
const replyToUser = ref(''); // 当前回复的用户名

// 当前回复的父评论id
const replyToParentId = ref<string | null>(null);
// 处理回复事件
const handleReply = (payload: { username: string; commentId: string }) => {
  replyToUser.value = payload.username;
  replyToParentId.value = payload.commentId;
};

// 取消回复
const cancelReply = () => {
  replyToUser.value = '';
};

// 处理评论提交
const handleCommentSubmit = async (comment: { text: string; replyTo: string; replyToParentId: string | null }) => {
  if (!comment.text.trim() || !postData.value) return;

  try {
    // 查找父评论 ID（如果是回复）
    // 直接用 InteractionBar 传来的 parentId
    const parentId: string | undefined = comment.replyToParentId || undefined;

    // 获取当前用户ID
    const userInfo = await getUserInfo();
    const misId = userInfo?.login || '';

    // 1. 先构造新评论对象
    const myUserInfo = await getUserInfo();
    const myUserId = myUserInfo?.login || '';
    const myName = myUserInfo?.mis_id_keyword || '我';
    const firstChar = currentUserId.charAt(0) || '我';
    const newComment = {
      comment_id: `temp_${Date.now()}`, // 临时ID，后面会被服务器返回的ID替换
      comment_mis_id: myUserId,
      comment_content: comment.text.trim(),
      comment_parent_id: parentId,
      create_time: new Date().toISOString(),
      avatar: firstChar,
      avatarColor: getRandomColor(myUserId),
    };

    // 2. 立即插入本地评论列表，实现"秒显"
    if (postData.value && Array.isArray(postData.value.comment_record)) {
      postData.value.comment_record.unshift(newComment);
    } else {
      postData.value.comment_record = [newComment];
    }

    // 3. 更新评论数量
    postData.value.comment_count = (postData.value.comment_count || 0) + 1;

    // 4. 调用后端接口存储评论
    const response = await storeComment(route.params.id as string, misId, comment.text.trim(), parentId);

    if (response && (response.status === 0 || response.code === 0)) {
      console.log('评论提交成功:', response);

      // 5. 更新临时评论的ID为服务器返回的ID
      if (response.data?.comment_id && postData.value.comment_record) {
        const tempComment = postData.value.comment_record.find((c) => c.comment_id === newComment.comment_id);
        if (tempComment) {
          tempComment.comment_id = response.data.comment_id;
        }
      }

      // 6. 延迟一段时间后再获取最新数据，确保服务器数据已更新
      setTimeout(() => {
        // 保存当前评论列表
        const currentComments = postData.value?.comment_record ? [...postData.value.comment_record] : [];

        // 获取最新数据
        void fetchPostDetail()
          .then(() => {
            // 如果获取数据后评论列表为空或长度减少，可能是服务器还未更新
            if (
              !postData.value?.comment_record ||
              (currentComments.length > 0 && postData.value.comment_record.length < currentComments.length)
            ) {
              postData.value.comment_record = currentComments;
            }
          })
          .catch((err) => {
            console.error('fetchPostDetail error:', err);
          });
      }, 2000); // 延迟2秒，给服务器足够时间处理
    } else {
      console.error('评论提交失败:', response);
      // 评论失败，从本地列表中移除临时评论
      if (postData.value.comment_record) {
        postData.value.comment_record = postData.value.comment_record.filter(
          (c) => c.comment_id !== newComment.comment_id,
        );
        // 恢复评论数量
        postData.value.comment_count = Math.max(0, (postData.value.comment_count || 1) - 1);
      }
    }
  } catch (error) {
    console.error('评论提交出错:', error);
  } finally {
    // 清空回复状态
    replyToUser.value = '';
    replyToParentId.value = null;
  }
};

// 处理评论数据，添加UI所需的字段并按层级结构排序
const processedComments = computed(() => {
  // 兼容 comment_record 为空对象或非数组
  const raw = postData.value?.comment_record;
  if (!raw || !Array.isArray(raw) || raw.length === 0) return [];

  // 深拷贝后再次判断类型，确保为数组
  const comments = JSON.parse(JSON.stringify(raw)) as ICommentItem[];
  if (!Array.isArray(comments)) return [];

  // 创建所有评论的映射，包括已删除的评论（用于保留子评论的引用关系）
  const allCommentsMap = new Map<string, ICommentItem>();
  comments.forEach((comment) => {
    allCommentsMap.set(comment.comment_id, comment);
  });

  // 过滤掉已删除的评论，无论是顶层评论还是子评论
  const filteredComments = comments.filter((comment) => comment.is_deleted !== true);

  // 为每个评论添加头像和颜色
  filteredComments.forEach((comment: ICommentItem) => {
    // 获取用户名首字母作为头像显示
    const firstChar = comment.comment_mis_id?.charAt(0) || '用';
    comment.avatar = firstChar;
    // 生成头像颜色
    comment.avatarColor = getRandomColor(comment.comment_mis_id || '用户');
  });

  // 按层级结构排序，子评论紧跟在父评论后面
  const result: ICommentItem[] = [];
  const parentMap = new Map<string, ICommentItem[]>();

  // 分组父子评论
  filteredComments.forEach((comment: ICommentItem) => {
    if (!comment.comment_parent_id) {
      // 父评论
      result.push(comment);
    } else {
      // 子评论
      if (!parentMap.has(comment.comment_parent_id)) {
        parentMap.set(comment.comment_parent_id, []);
      }
      parentMap.get(comment.comment_parent_id)!.push(comment);
    }
  });

  // 新的两层评论分组逻辑
  // 1. 建立id到评论的映射（包括已删除的评论，用于处理引用关系）
  const idMap = new Map<string, ICommentItem>();
  comments.forEach((c: ICommentItem) => {
    idMap.set(c.comment_id, c);
  });

  // 2. 找到每条评论的最顶层父评论id（即使中间有已删除的评论）
  function findTopParentId(comment: ICommentItem): string | null {
    const cur = comment;
    const parentId = cur.comment_parent_id;

    // 如果没有父评论，返回自己的ID
    if (!parentId) return cur.comment_id;

    // 查找顶层父评论，即使中间有已删除的评论
    let currentParentId = parentId;
    let topParentId: string | null = null;

    while (currentParentId) {
      const parent = idMap.get(currentParentId);
      if (!parent) break;

      // 如果父评论没有父评论（即顶层评论）
      if (!parent.comment_parent_id) {
        // 如果顶层评论没有被删除，则使用它作为分组依据
        if (parent.is_deleted !== true) {
          topParentId = parent.comment_id;
        }
        break;
      }

      // 继续向上查找
      currentParentId = parent.comment_parent_id;
    }

    return topParentId;
  }

  // 3. 用顶层父评论id分组
  const groupMap = new Map<string, ICommentItem[]>();

  // 先处理顶层评论（没有父评论的评论）
  const topLevelComments = filteredComments.filter((c) => !c.comment_parent_id);
  topLevelComments.forEach((c) => {
    groupMap.set(c.comment_id, [c]);
  });

  // 处理子评论（有父评论的评论）
  filteredComments
    .filter((c) => c.comment_parent_id)
    .forEach((c) => {
      // 找到这条评论的顶层父评论
      const topId = findTopParentId(c);

      // 如果找到了有效的顶层父评论，将这条评论添加到对应的组
      if (topId && groupMap.has(topId)) {
        groupMap.get(topId)!.push(c);
      } else {
        // 如果没有找到有效的顶层父评论（可能是因为顶层评论被删除了）
        // 但这条评论仍然需要显示，我们创建一个新的组

        // 查找这条评论直接回复的评论
        const directParentId = c.comment_parent_id;
        const directParent = idMap.get(directParentId || '');

        // 如果直接父评论存在但已被删除，我们需要显示一个占位符
        if (directParent && directParent.is_deleted) {
          // 创建一个虚拟的组，使用这条评论的ID作为组ID
          if (!groupMap.has(c.comment_id)) {
            groupMap.set(c.comment_id, [c]);
          }
        }
      }
    });

  // 4. 获取所有顶层父评论，按时间排序
  const sortedTopLevelComments = topLevelComments.sort(
    (a, b) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
  );

  // 5. 按顺序平铺：父评论+其所有子评论（按时间排序）
  const finalResult: ICommentItem[] = [];

  // 先添加所有顶层评论及其子评论
  sortedTopLevelComments.forEach((parent) => {
    const group = groupMap.get(parent.comment_id) || [];

    // 父评论排最前，剩下的子评论按时间排序
    const children = group
      .filter((c) => c.comment_id !== parent.comment_id)
      .sort((a, b) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime());

    finalResult.push(parent, ...children);
  });

  return finalResult;
});

// 格式化评论时间
const formatCommentTime = (timeString: string) => {
  try {
    const date = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return '刚刚';
    }
    if (diffMin < 60) {
      return `${diffMin} 分钟前`;
    }
    if (diffHour < 24) {
      return `${diffHour} 小时前`;
    }
    if (diffDay < 30) {
      return `${diffDay} 天前`;
    }
    // 超过30天则显示具体日期
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return timeString;
  }
};

// 返回上一页
const goBack = () => {
  void router.back();
};

// 切换点赞状态
const toggleLike = async () => {
  if (!postData.value) return;

  try {
    // 获取用户信息
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.login) {
      console.error('获取用户信息失败');
      return;
    }

    const misId = userInfo.login;
    const shareId = route.params.id as string; // 使用路由参数中的ID

    if (isLiked.value) {
      // 已点赞，执行取消点赞
      const response = await unlikeShare(shareId, misId);
      const isSuccess = response && (response.code === 0 || response.code === 200 || response.status === 0);

      if (isSuccess) {
        isLiked.value = false;
        if (postData.value.like_count && postData.value.like_count > 0) {
          postData.value.like_count -= 1;
        }
        console.log('取消点赞成功');
      } else {
        console.error('取消点赞失败:', response?.message || '未知错误');
      }
    } else {
      // 未点赞，执行点赞
      const response = await likeShare(shareId, misId);
      const isSuccess =
        response && (response.code === 0 || response.code === 200 || response.status === 0 || response.status === 300);

      if (isSuccess) {
        isLiked.value = true;
        if (postData.value.like_count !== undefined) {
          postData.value.like_count += 1;
        } else {
          postData.value.like_count = 1;
        }

        // 如果同时点了踩，则先取消点踩再点赞
        if (isDisliked.value) {
          // 先调用API取消点踩
          await undislikeShare(shareId, misId);

          isDisliked.value = false;
          if (postData.value.dislike_count && postData.value.dislike_count > 0) {
            postData.value.dislike_count -= 1;
          }
        }

        console.log('点赞成功');
      } else {
        console.error('点赞失败:', response?.message || '未知错误');
      }
    }
  } catch (error) {
    console.error('点赞操作出错:', error);
  }
};

// 切换点踩状态
const toggleDislike = async () => {
  if (!postData.value) return;

  try {
    // 获取用户信息
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.login) {
      console.error('获取用户信息失败');
      return;
    }

    const misId = userInfo.login;
    const shareId = route.params.id as string; // 使用路由参数中的ID

    if (isDisliked.value) {
      // 已点踩，执行取消点踩
      const response = await undislikeShare(shareId, misId);
      const isSuccess = response && (response.code === 0 || response.code === 200 || response.status === 0);

      if (isSuccess) {
        isDisliked.value = false;
        if (postData.value.dislike_count && postData.value.dislike_count > 0) {
          postData.value.dislike_count -= 1;
        }
        console.log('取消点踩成功');
      } else {
        console.error('取消点踩失败:', response?.message || '未知错误');
      }
    } else {
      // 未点踩，执行点踩
      const response = await dislikeShare(shareId, misId);
      const isSuccess =
        response && (response.code === 0 || response.code === 200 || response.status === 0 || response.status === 300);

      if (isSuccess) {
        isDisliked.value = true;
        if (postData.value.dislike_count !== undefined) {
          postData.value.dislike_count += 1;
        } else {
          postData.value.dislike_count = 1;
        }

        // 如果同时点了赞，则先取消点赞再点踩
        if (isLiked.value) {
          // 先调用API取消点赞
          await unlikeShare(shareId, misId);

          isLiked.value = false;
          if (postData.value.like_count && postData.value.like_count > 0) {
            postData.value.like_count -= 1;
          }
        }

        console.log('点踩成功');
      } else {
        console.error('点踩失败:', response?.message || '未知错误');
      }
    }
  } catch (error) {
    console.error('点踩操作出错:', error);
  }
};

// 获取帖子详情数据
const fetchPostDetail = async () => {
  // 校验分享ID有效性
  const shareId = route.params.id as string;
  if (!shareId || typeof shareId !== 'string' || shareId === 'undefined') {
    console.warn('无效的shareId，跳过详情请求', shareId);
    return;
  }
  try {
    // 获取用户信息
    const userInfo = await getUserInfo();
    const misId = userInfo?.login || '';

    // 获取帖子详情
    const response = await getShareDetail(shareId, misId);
    if (response && response.status === 0 && response.data) {
      // 获取用户名首字母作为头像显示
      const firstChar = response.data.mis_id_keyword?.charAt(0) || '用';

      // 确保food_img_urls至少有一个元素
      const foodImgUrls = response.data.food_img_urls || [null];
      if (foodImgUrls.length === 0) {
        foodImgUrls.push(null);
      }

      // 保存当前已删除的评论ID列表，以便在更新数据后保持这些评论的删除状态
      const deletedCommentIds = new Set<string>();
      if (postData.value?.comment_record && Array.isArray(postData.value.comment_record)) {
        postData.value.comment_record.forEach((comment) => {
          if (comment.is_deleted) {
            deletedCommentIds.add(comment.comment_id);
          }
        });
      }

      postData.value = {
        ...response.data,
        food_img_urls: foodImgUrls,
        imageUrl: defaultDish, // 保留原有字段，用于兼容
        avatar: firstChar,
        username: response.data.mis_id_keyword || '用户',
        avatarColor: getRandomColor(response.data.mis_id_keyword || '用户'),
      };

      // 恢复已删除评论的状态
      if (postData.value.comment_record && Array.isArray(postData.value.comment_record) && deletedCommentIds.size > 0) {
        postData.value.comment_record.forEach((comment) => {
          if (deletedCommentIds.has(comment.comment_id)) {
            comment.is_deleted = true;
          }
        });
      }

      // 初始化点赞和点踩状态
      // 直接使用API返回的状态
      isLiked.value = response.data.is_liked || false;
      isDisliked.value = response.data.is_disliked || false;

      console.log('帖子数据加载成功:', postData.value);
    } else {
      console.error('获取帖子详情失败:', response);
    }
  } catch (error) {
    console.error('获取帖子详情出错:', error);
  }
};

onMounted(() => {
  void fetchPostDetail();

  // 初始化容器宽度
  window.addEventListener('resize', initContainerWidth);
  void nextTick(() => {
    initContainerWidth();
  });

  // 禁用整个容器的默认拖拽行为
  document.addEventListener('dragstart', (e) => {
    if (e.target instanceof HTMLImageElement) {
      e.preventDefault();
    }
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', initContainerWidth);
});

// 处理删除评论
const handleDeleteComment = async (comment: ICommentItem) => {
  try {
    const commentsToDelete: string[] = [comment.comment_id];

    // 如果是顶层评论，需要找出并删除所有子评论
    if (!comment.comment_parent_id && postData.value?.comment_record) {
      // 找出所有子评论
      const childComments = postData.value.comment_record.filter((c) => {
        // 检查是否是当前评论的直接子评论或间接子评论
        const checkIsChildComment = (checkComment: ICommentItem): boolean => {
          if (!checkComment.comment_parent_id) return false;

          // 直接子评论
          if (checkComment.comment_parent_id === comment.comment_id) return true;

          // 查找父评论
          const parent = postData.value?.comment_record?.find((p) => p.comment_id === checkComment.comment_parent_id);

          if (!parent) return false;

          // 如果找到的父评论是当前要删除的评论，那么这个评论也需要删除
          if (parent.comment_id === comment.comment_id) return true;

          // 递归检查父评论的父评论
          return checkIsChildComment(parent);
        };

        return checkIsChildComment(c);
      });

      // 将所有子评论ID加入删除列表
      childComments.forEach((c) => commentsToDelete.push(c.comment_id));
    }

    // 在UI上立即标记评论为已删除
    if (postData.value?.comment_record) {
      // 如果是子评论，我们不从UI中移除它，而是将其标记为已删除
      if (comment.comment_parent_id) {
        // 找到要删除的评论，将其标记为已删除，但保留在数组中
        const commentToDelete = postData.value.comment_record.find((c) => c.comment_id === comment.comment_id);
        if (commentToDelete) {
          // 备份评论内容，以便后续显示
          commentToDelete.original_content = commentToDelete.comment_content;
          // 将评论标记为已删除，但不从数组中移除
          commentToDelete.is_deleted = true;
          commentToDelete.comment_content = '该评论已删除';
        }

        // 无论是顶层评论还是子评论，都需要更新评论计数
        if (postData.value.comment_count) {
          postData.value.comment_count = Math.max(0, postData.value.comment_count - 1);
        }
      } else {
        // 如果是顶层评论，则按原来的逻辑处理：从数组中移除
        postData.value.comment_record = postData.value.comment_record.filter(
          (c) => !commentsToDelete.includes(c.comment_id),
        );

        // 更新评论计数
        if (postData.value.comment_count) {
          postData.value.comment_count = Math.max(0, postData.value.comment_count - commentsToDelete.length);
        }
      }
    }

    // 调用API删除评论（统一使用批量删除接口）
    const response = await deleteComments(route.params.id as string, commentsToDelete);

    // 检查删除是否成功
    if (response.status !== 0) {
      console.error('删除失败:', response);
      showFailToast('删除失败');
      // 如果删除失败，需要恢复评论数据
      // 但由于我们已经从数组中移除了评论，这里需要重新获取帖子数据
      await fetchPostDetail();
    } else {
      console.log('删除成功');
      showSuccessToast('删除成功');
    }
  } catch (error) {
    console.error('删除评论失败:', error);
    showFailToast('删除失败');

    // 发生错误时，重新获取帖子数据以恢复状态
    await fetchPostDetail();
  }
};
</script>

<style scoped>
.modal-mask {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.32);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-dialog {
  background: #fff;
  border-radius: 16px;
  padding: 32px 24px 20px;
  min-width: 260px;
  max-width: 80vw;
  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.modal-title {
  font-size: 20px;
  color: #333;
  margin-bottom: 24px;
  font-weight: 500;
}
.modal-actions {
  display: flex;
  gap: 24px;
}
.modal-btn {
  min-width: 72px;
  padding: 6px 20px;
  border-radius: 6px;
  border: none;
  font-size: 16px;
  cursor: pointer;
}
.modal-btn.cancel {
  background: #f0f0f0;
  color: #666;
}
.modal-btn.confirm {
  background: #ff6b6b;
  color: #fff;
}
.modal-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

<style lang="scss" scoped>
.post-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
  overflow-y: auto;
  max-width: 750px;
  margin: 0 auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .header {
    display: flex;
    align-items: center;
    height: 90px;
    background-color: #fff;
    position: fixed;
    padding: 20px 25px;
    width: 100%;
    max-width: 750px;
    margin: 0 auto;
    z-index: 10;

    .back-button {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      cursor: pointer;
      margin-right: 12px;
    }

    .user-info {
      display: flex;
      align-items: center;
      flex: 1;

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 22px;
        font-weight: 500;
        margin-right: 15px;
      }

      .username {
        font-size: 28px;
        font-weight: 600;
        max-width: 300px;
        color: #2c2c2c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
      }
    }

    .placeholder {
      width: 44px;
    }

    .follow-btn {
      padding: 4px 20px;
      font-size: 25px;
      border-radius: 30px;
      border: none;
      outline: none;
      background-color: #2ec398;
      color: #fff;
      cursor: pointer;
      transition:
        background 0.2s,
        color 0.2s;
      font-weight: 500;
      min-width: 70px;
      height: 40px;
      box-shadow: 0 2px 8px 0 rgba(46, 195, 152, 0.08);
    }
    .follow-btn:active {
      background-color: #1ea67a;
    }
    .follow-btn.followed {
      background-color: #e5e5e5;
      color: #888;
      cursor: pointer;
      border: none;
    }
    .follow-btn.followed:active {
      background-color: #d8d8d8;
      color: #888;
    }
  }

  .post-content {
    padding: 100px 35px 20px;

    .post-image {
      margin-bottom: 20px;
      border-radius: 25px;
      overflow: hidden;
      position: relative;

      .image-indicator {
        position: absolute;
        top: 25px;
        right: 25px;
        background-color: rgba(77, 77, 77, 0.6);
        color: white;
        padding: 7px 13px;
        border-radius: 20px;
        font-size: 20px;
        font-weight: 500;
        z-index: 5;
      }

      .image-swiper {
        width: 100%;
        overflow: hidden;
        border-radius: 25px;
        position: relative;
        cursor: grab; /* 默认显示抓取样式，表明可以拖动 */
        &:active {
          cursor: grabbing; /* 拖拽时显示抓取样式 */
        }
      }

      .swiper-wrapper {
        display: flex;
        transition: transform 0.3s ease;
        width: 100%;
        height: 100%;
      }

      .swiper-slide {
        flex: 0 0 100%;
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;

        .slide-content {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .swipe-img {
        width: 100%;
        height: auto;
        object-fit: contain;
        display: block;
        border-radius: 25px;
        user-select: none;
        -webkit-user-drag: none;
        cursor: pointer; /* 添加指针样式，表明图片可点击 */
      }
    }

    .order-detail-container {
      width: 100%;
      margin: 15px 0 20px;
      padding: 0;
      border-radius: 25px;
      overflow: hidden;
      transition: transform 0.3s ease; /* 添加过渡效果，使变化更平滑 */
      transform-origin: center; /* 设置变化的原点为中心 */
      will-change: transform; /* 提示浏览器该元素的transform属性将会变化，优化性能 */
    }

    .order-detail {
      width: 100%;

      .link-button {
        display: flex;
        align-items: center;
        background-color: #f0f0f0;
        padding: 15px 20px;
        border-radius: 0;
        color: #333;
        text-decoration: none;
        border: none;

        .shop-icon {
          margin-right: 15px;
          width: 60px;
          height: 60px;
          border-radius: 10px;
          overflow: hidden;
          border: 1px solid #eaeaea;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .order-info {
          flex: 1;

          .order-name {
            font-size: 25px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #444;
            line-height: 1.3;
          }

          .order-price {
            font-size: 22px;
            color: #666;
            font-weight: 600;
          }
        }
      }
    }

    .shop-name {
      margin-top: 20px;
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: 400;
      color: #8a8a8a;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .location-icon {
        display: flex;
        align-items: center;
        margin-right: 5px;
      }
    }

    .post-title {
      font-size: 40px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      line-height: 1.4;
    }

    .post-body {
      font-size: 32px;
      line-height: 1.7;
      color: #2c2c2c;
      white-space: pre-line;
      word-break: break-word;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 135px 0 40px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #333333;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    span {
      font-size: 25px;
      color: #666;
      font-weight: 500;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

/* 底部交互区域样式 */
.interaction-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .interaction-buttons {
    display: flex;
    gap: 20px;

    .interaction-button {
      display: flex;
      align-items: center;
      gap: 5px;
      padding: 5px 10px;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s ease;

      svg {
        transition: all 0.2s ease;
      }

      span {
        font-size: 14px;
        color: #666;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      &:active svg {
        transform: scale(0.9);
      }

      &.active span {
        font-weight: 500;
      }
    }
  }

  .comment-input-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .comment-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      flex-shrink: 0;
    }

    .comment-input-wrapper {
      flex: 1;
      position: relative;
      display: flex;

      .comment-input {
        flex: 1;
        height: 36px;
        border: 1px solid #e0e0e0;
        border-radius: 18px;
        padding: 0 40px 0 15px;
        font-size: 14px;
        outline: none;

        &:focus {
          border-color: #4ecdc4;
        }

        &::placeholder {
          color: #999;
        }
      }

      .comment-submit {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        border: none;
        background: transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4ecdc4;
        padding: 0;

        &:disabled {
          color: #ccc;
          cursor: not-allowed;
        }

        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

/* 为了防止底部内容被交互区域遮挡，添加底部留白 */
.post-content {
  padding-bottom: 130px !important;
}

/* 评论区样式 */
.comment-section {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;

  .comment-count {
    font-size: 16px;
    color: #333;
    font-weight: 500;
    margin-bottom: 15px;
    text-align: left;
  }

  .comment-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .comment-item {
    display: flex;
    align-items: flex-start;
    padding: 10px 0;

    &.comment-reply {
      margin-left: 40px;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        left: -20px;
        top: 20px;
        width: 15px;
        height: 1px;
        background-color: #ddd;
      }
    }

    .comment-avatar {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
      flex-shrink: 0;
      margin-right: 10px;
    }

    .comment-content {
      flex: 1;

      .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        .comment-username {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }

        .comment-time {
          font-size: 12px;
          color: #999;
        }
      }

      .comment-text {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        word-break: break-word;
      }
    }
  }

  .no-comments {
    text-align: center;
    color: #999;
    font-size: 14px;
    padding: 20px 0;
  }
}

/* 全屏查看图片样式 - 放在外部以确保它应用到 teleport 到 body 的元素 */
.fullscreen-image {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(109, 109, 109, 0.75); /* 背景透明度调整，不再是纯黑 */
  backdrop-filter: blur(8px); /* 添加背景模糊效果 */
  -webkit-backdrop-filter: blur(8px); /* Safari 兼容性 */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 单独设置容器样式，避免嵌套选择器可能导致的问题 */
.fullscreen-image-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 单独设置放大图片样式 */
.zoomed-image {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 25px; /* 添加圆角，与帖子图片保持一致 */
}

/* 单独设置关闭按钮样式 */
.close-button {
  position: absolute;
  top: 40px;
  right: 40px; /* 将关闭按钮移到右侧 */
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2); /* 半透明白色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3); /* 悬停时变亮 */
  transform: scale(1.1); /* 悬停时缩放效果 */
}
.action-btn.no-style {
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  box-shadow: none;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.action-btn.no-style:active {
  background: transparent;
}

.action-sheet-mask {
  position: fixed;
  z-index: 2002;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: transparent;
  /* 去掉flex布局，防止弹窗随父级漂移 */
}
.action-sheet-popup {
  min-width: 140px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  z-index: 2003;
}
.action-sheet-item {
  padding: 18px 24px;
  font-size: 24px;
  color: #222;
  cursor: pointer;
  background: transparent;
  transition: background 0.15s;
}
.action-sheet-item:hover {
  background: #f5f5f5;
}
.action-sheet-item.danger {
  color: #e74c3c;
}
</style>
