<template>
  <div class="search-container">
    <!-- 头部搜索区域 -->
    <div class="fade-in">
      <SearchHeader @search="handleSearch" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { RouteName } from '@/constants/route-name';
import SearchHeader from './components/SearchHeader.vue';

const router = useRouter();

// 处理搜索
const handleSearch = (keyword: string) => {
  void router.push({
    name: RouteName.SHARE_SEARCH_RESULT,
    query: {
      keyword,
    },
  });
};
</script>

<style lang="scss" scoped>
.search-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-image: url(@/assets/img/page-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  .fade-in {
    animation: fadeIn 0.15s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
