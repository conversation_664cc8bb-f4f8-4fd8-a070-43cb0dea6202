<template>
  <div class="app-container">
    <div class="v-chat-container">
      <div class="v-chat">
        <!-- 头部区域替换为Maintopbar组件 -->
        <Maintopbar
          :show-history-btn="false"
          :show-user-avatar="true"
          :show-assistant-avatar="false"
          :show-voice-btn="false"
          :show-add-chat-btn="true"
          add-chat-type="search"
          :is-chat-play="false"
          :user-loading="userLoading"
          :current-mis-id="currentMisId"
          :get-random-color="getRandomColor"
          :get-avatar-letter="getAvatarLetter"
          :active-tab="activeTab"
          :show-header-grad="false"
          @switch-tab="switchTab"
          @avatar-click="goToUserProfile"
          @add-chat="goToSearch"
        />

        <!-- 瀑布流内容区域 -->
        <div ref="contentWrapper" class="content-wrapper">
          <div ref="scrollableContent" class="scrollable-content">
            <div ref="shareContainerRef" class="share-container">
              <div ref="waterfallRef" class="waterfall-container">
                <div class="waterfall-column left-column">
                  <div v-for="item in leftItems" :key="item.id" class="waterfall-item" @click="handleItemClick(item)">
                    <div class="item-image">
                      <img
                        :src="getCoverImage(item.food_img_urls)"
                        :style="getImageStyle(getCoverImage(item.food_img_urls))"
                        alt="分享图片"
                        @load="handleImageLoad"
                      />
                    </div>
                    <div class="item-content">
                      <div class="item-title">{{ item.title }}</div>
                      <div class="item-text">{{ item.content }}</div>
                      <div class="item-info">
                        <div class="user-info" @click.stop="goToUserProfileById(item.username)">
                          <div class="avatar" :style="{ backgroundColor: getRandomColor(item.username) }">
                            {{ getAvatarLetter(item.username) }}
                          </div>
                          <span class="username" :title="item.username">{{ item.username }}</span>
                        </div>
                        <div class="action-buttons">
                          <div class="like-container" @click.stop="toggleLike(item)">
                            <span class="like-icon" :class="{ liked: item.isLiked }"></span>
                            <span class="like-count">{{ formatLikeCount(item.likeCount) }}</span>
                          </div>
                          <div class="dislike-container" @click.stop="toggleDislike(item)">
                            <span class="dislike-icon" :class="{ disliked: item.isDisliked }"></span>
                            <span class="dislike-count">{{ formatLikeCount(item.dislikeCount) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="waterfall-column right-column">
                  <div v-for="item in rightItems" :key="item.id" class="waterfall-item" @click="handleItemClick(item)">
                    <div class="item-image">
                      <img
                        :src="getCoverImage(item.food_img_urls)"
                        :style="getImageStyle(getCoverImage(item.food_img_urls))"
                        alt="分享图片"
                        @load="handleImageLoad"
                      />
                    </div>
                    <div class="item-content">
                      <div class="item-title">{{ item.title }}</div>
                      <div class="item-text">{{ item.content }}</div>
                      <div class="item-info">
                        <div class="user-info" @click.stop="goToUserProfileById(item.username)">
                          <div class="avatar" :style="{ backgroundColor: getRandomColor(item.username) }">
                            {{ getAvatarLetter(item.username) }}
                          </div>
                          <span class="username" :title="item.username">{{ item.username }}</span>
                        </div>
                        <div class="action-buttons">
                          <div class="like-container" @click.stop="toggleLike(item)">
                            <span class="like-icon" :class="{ liked: item.isLiked }"></span>
                            <span class="like-count">{{ formatLikeCount(item.likeCount) }}</span>
                          </div>
                          <div class="dislike-container" @click.stop="toggleDislike(item)">
                            <span class="dislike-icon" :class="{ disliked: item.isDisliked }"></span>
                            <span class="dislike-count">{{ formatLikeCount(item.dislikeCount) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 加载更多 -->
              <div v-if="loading" class="loading-more">
                <div class="loading-spinner"></div>
                <span>加载中...</span>
              </div>

              <!-- 观察元素，用于触发加载更多 -->
              <div ref="observerTarget" class="observer-target"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <TabBar default-tab="home" />
</template>

<script setup lang="ts">
import Maintopbar from '@/components/Maintopbar.vue';
import { ref, onMounted, onUnmounted, computed, reactive, onActivated, onDeactivated, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { RouteName } from '@/constants/route-name';
import {
  getShareListByPage,
  likeShare,
  unlikeShare,
  dislikeShare,
  undislikeShare,
  IShareItem as IShareItemApi,
} from '@/apis/share';
import { getUserInfo } from '@/apis/common';
import { getRandomColor, getAvatarLetter } from '@/utils/avatar';
import TabBar from '@/pages/share/components/TabBar.vue';

// 默认图片导入
import defaultDish from '@/assets/img/default-dish.jpeg';

// 页面展示数据结构
interface IShareItem {
  id: string;
  title: string;
  content: string;
  username: string;
  time: string;
  likeCount: number;
  isLiked: boolean; // 记录用户是否已经点赞
  dislikeCount: number;
  isDisliked: boolean; // 记录用户是否已经点踩
  food_img_urls: (string | null)[];
}

const loading = ref(false);
const allItems = ref<IShareItem[]>([]);
const imageRatios = reactive<Record<string, number>>({});

// 处理图片加载完成事件
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  const url = img.src;
  const ratio = img.naturalWidth / img.naturalHeight;
  imageRatios[url] = ratio;
};

// 获取图片样式
const getImageStyle = (imageUrl: string) => {
  // 如果图片还没加载完，返回默认样式
  if (!imageRatios[imageUrl]) {
    return {};
  }

  const ratio = imageRatios[imageUrl];

  // 横图，宽高比大于4:3的裁剪为4:3
  if (ratio > 1) {
    if (ratio > 4 / 3) {
      return { aspectRatio: '4/3' };
    }
  }
  // 竖图，宽高比小于3:4的裁剪为3:4
  else if (ratio < 3 / 4) {
    return { aspectRatio: '3/4' };
  }

  // 如果在允许范围内，保持原始比例
  return { height: 'auto' };
};

// 获取封面图片
const getCoverImage = (urls: (string | null)[] = []): string => {
  return urls.find((url) => !!url) || defaultDish;
};

// 路由相关
const router = useRouter();
const route = useRoute();
const activeTab = ref('share');

// 当前用户ID
const currentMisId = ref('liulingfeng05'); // 默认值，会被更新
const userLoading = ref(true); // 用户信息加载中标记

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    const userInfo = await getUserInfo();
    if (userInfo && userInfo.login) {
      currentMisId.value = userInfo.login;
      console.log('当前用户ID:', currentMisId.value);
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  } finally {
    userLoading.value = false;
  }
};

// 跳转到当前用户个人页面
const goToUserProfile = () => {
  console.log('跳转到当前用户页面', currentMisId.value);
  // 跳转到当前登录用户的个人页面，并携带 misid 参数
  void router.push({
    name: RouteName.USER_PROFILE,
    params: { id: currentMisId.value },
  });

  // 保存当前用户ID到本地存储，以便在个人页面使用
  localStorage.setItem('currentMisId', currentMisId.value);
};

// 跳转到指定用户的个人页面
const goToUserProfileById = (userId: string) => {
  console.log('跳转到指定用户页面', userId);
  // 跳转到指定用户的个人页面
  void router.push({
    name: RouteName.USER_PROFILE,
    params: { id: userId },
  });
};

// 更新标签样式的函数
const updateTabStyle = (tab: string) => {
  setTimeout(() => {
    const chatBtn = document.querySelector('.tab-switch .tab-btn:first-child');
    const shareBtn = document.querySelector('.tab-switch .tab-btn:last-child');

    if (chatBtn && shareBtn) {
      if (tab === 'chat') {
        chatBtn.classList.add('active');
        shareBtn.classList.remove('active');
      } else {
        chatBtn.classList.remove('active');
        shareBtn.classList.add('active');
      }
      console.log('已直接设置标签状态为:', tab);
    }
  }, 0);
};

// 监听路由变化，更新标签状态
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log('路由变化，新路径:', newPath);
    // 根据路径更新活动标签
    if (newPath.includes('/chat')) {
      activeTab.value = 'chat';
    } else if (newPath.includes('/share') || newPath === '/') {
      activeTab.value = 'share';
    }

    // 仅当从 chat 路由跳转到 share 时刷新列表
    if (newPath.includes('/share') && oldPath && oldPath.includes('/chat')) {
      offset.value = 0;
      hasMore.value = true;
      allItems.value = [];
      void fetchData();
    }

    // 直接更新DOM元素的样式
    updateTabStyle(activeTab.value);
  },
  { immediate: true }, // 立即执行一次
);

// 切换标签
const switchTab = (tab: string) => {
  // 更新活动标签
  activeTab.value = tab;

  // 点击share标签时刷新内容
  if (tab === 'share') {
    offset.value = 0;
    hasMore.value = true;
    allItems.value = [];
    void fetchData();
  }

  // 如果切换到chat且当前在share，清除分享滚动记录
  if (tab === 'chat' && route.path.includes('/share')) {
    localStorage.removeItem('shareScrollPosition');
    scrollPosition.value = 0;
  }

  // 直接更新标签样式
  updateTabStyle(tab);

  // 跳转
  void router.push(`/${tab}`);
};

// 分为左右两列
const leftItems = computed(() => {
  return allItems.value.filter((_, index) => index % 2 === 0);
});

const rightItems = computed(() => {
  return allItems.value.filter((_, index) => index % 2 === 1);
});

// 获取数据
const offset = ref(0);
const batchSize = ref(10);
const hasMore = ref(true);

const fetchData = async () => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;

  try {
    const response = await getShareListByPage({
      offset: offset.value,
      batch_size: batchSize.value,
    });

    if (response.code === 200 && response.data) {
      const { shares, total } = response.data;

      // 判断是否还有更多数据
      hasMore.value = offset.value + shares.length < total;

      // 将API返回的数据转换为页面展示所需的格式
      const newItems: IShareItem[] = shares.map((item: IShareItemApi) => {
        return {
          id: item.id,
          title: item.title,
          content: item.content,
          username: item.mis_id_keyword || '用户',
          time: item.time,
          likeCount: item.like_count || 0,
          isLiked: item.is_liked || false, // 使用API返回的is_liked字段
          dislikeCount: item.dislike_count || 0, // 使用API返回的dislike_count字段
          isDisliked: item.is_disliked || false, // 使用API返回的is_disliked字段
          food_img_urls: item.food_img_urls || [],
        };
      });

      if (offset.value === 0) {
        allItems.value = newItems;
      } else {
        allItems.value = [...allItems.value, ...newItems];
      }

      // 更新偏移量
      offset.value += shares.length;
    } else {
      console.error('获取分享列表失败:', response.message);
    }
  } catch (error) {
    console.error('获取分享列表出错:', error);
  } finally {
    loading.value = false;
  }
};

// 使用IntersectionObserver监听元素是否可见
const observerTarget = ref<HTMLElement | null>(null);
let observer: IntersectionObserver | null = null;

// 创建观察器
const setupIntersectionObserver = () => {
  if (!observerTarget.value) return;

  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0];
      if (entry.isIntersecting && !loading.value && hasMore.value) {
        console.log('观察器触发加载，当前偏移量:', offset.value);
        void fetchData();
      }
    },
    { threshold: 0.1 },
  );

  observer.observe(observerTarget.value);
  console.log('观察器已设置');
};

// 点击卡片
const handleItemClick = (item: IShareItem) => {
  console.log('点击了卡片:', item);

  // 在跳转前保存滚动位置
  if (scrollableContent.value) {
    scrollPosition.value = scrollableContent.value.scrollTop;
    console.log('点击卡片时保存滚动位置:', scrollPosition.value);
    // 将滚动位置存入localStorage作为备份
    localStorage.setItem('shareScrollPosition', scrollPosition.value.toString());
  }

  // 跳转到帖子详情页
  void router
    .push({
      name: RouteName.POST_DETAIL,
      params: { id: item.id.toString() },
    })
    .catch((error) => {
      console.error('跳转失败:', error);
    });
};

onMounted(() => {
  // 获取当前用户信息
  void fetchCurrentUser();
  // 初始加载数据
  void fetchData();

  // 设置交叉观察器
  setTimeout(() => {
    setupIntersectionObserver();
  }, 500); // 给一些时间确保元素已渲染

  // 初始化标签状态
  setTimeout(() => {
    if (route.path.includes('/chat')) {
      activeTab.value = 'chat';
    } else {
      activeTab.value = 'share';
    }
    updateTabStyle(activeTab.value);
  }, 100);
});

// 保存滚动位置
const scrollPosition = ref(0);
const vChatRef = ref<HTMLElement | null>(null);
const scrollableContent = ref<HTMLElement | null>(null);
const shareContainerRef = ref<HTMLElement | null>(null);

// 初始渲染标记，用于避免闪烁
// 如果是初次渲染，则不设置滚动位置
const isInitialRender = ref(true);

// 在模板初始渲染前设置滚动位置
const setInitialScrollPosition = () => {
  // 直接从 localStorage 中恢复滚动位置
  const savedPosition = localStorage.getItem('shareScrollPosition');
  if (savedPosition) {
    scrollPosition.value = parseInt(savedPosition, 10);
    console.log('初始化滚动位置:', scrollPosition.value);

    // 将初始滚动位置应用到CSS变量
    if (typeof document !== 'undefined') {
      document.documentElement.style.setProperty('--initial-scroll-top', `${scrollPosition.value}px`);
    }
  }
};

// 格式化点赞数
const formatLikeCount = (count: number): string => {
  if (count >= 10000) {
    // 超过1万，使用万作为单位，保留一位小数
    const wan = Math.floor(count / 1000) / 10;
    // 如果小数点后是0，则去掉小数点
    return wan % 1 === 0 ? `${Math.floor(wan)}万` : `${wan}万`;
  }
  // 千及以下用4位数展示
  return count.toString();
};

// 切换点赞状态
const toggleLike = async (item: IShareItem) => {
  try {
    // 先立即更新UI状态，提供即时反馈
    const originalLikeCount = item.likeCount;
    const originalIsLiked = item.isLiked;

    // 获取用户ID - 从getUserInfo接口获取login字段
    const userInfo = await getUserInfo();
    console.log('getUserInfo返回值:', userInfo);
    console.log('login字段:', userInfo.login);
    const misId = userInfo.login || 'liulingfeng05';
    console.log('最终使用的misId:', misId);

    // 先切换状态
    item.isLiked = !item.isLiked;

    if (originalIsLiked) {
      // 如果之前已经点赞，则取消点赞
      item.likeCount = Math.max(0, item.likeCount - 1); // 确保不会出现负数
      // 发送API请求 - 使用新接口
      const response = await unlikeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已取消点赞）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isLiked = originalIsLiked;
        item.likeCount = originalLikeCount;
        console.error('取消点赞失败:', response);
      }
      console.log('取消点赞成功:', response);
    } else {
      // 如果之前未点赞，则添加点赞
      item.likeCount += 1;
      // 发送API请求 - 使用新接口
      const response = await likeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已点赞）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isLiked = originalIsLiked;
        item.likeCount = originalLikeCount;
        console.error('点赞失败:', response);
      }
      console.log('点赞成功:', response);

      // 如果同时点了踩，则先取消点踩再点赞
      if (item.isDisliked) {
        // 先更新UI状态
        const originalDislikeCount = item.dislikeCount;
        item.isDisliked = false;
        item.dislikeCount = Math.max(0, item.dislikeCount - 1);

        // 发送API请求取消点踩
        try {
          await undislikeShare(item.id, misId);
        } catch (error) {
          console.error('取消点踩失败:', error);
          // 如果取消点踩失败，不恢复状态，因为点赞已经成功
        }
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
  }
};

// 切换点踩状态
const toggleDislike = async (item: IShareItem) => {
  try {
    // 先立即更新UI状态，提供即时反馈
    const originalDislikeCount = item.dislikeCount;
    const originalIsDisliked = item.isDisliked;

    // 获取用户ID - 从getUserInfo接口获取login字段
    const userInfo = await getUserInfo();
    const misId = userInfo.login || 'liulingfeng05';

    // 先切换状态
    item.isDisliked = !item.isDisliked;

    if (originalIsDisliked) {
      // 如果之前已经点踩，则取消点踩
      item.dislikeCount = Math.max(0, item.dislikeCount - 1); // 确保不会出现负数
      // 发送API请求
      const response = await undislikeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已取消点踩）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isDisliked = originalIsDisliked;
        item.dislikeCount = originalDislikeCount;
        console.error('取消点踩失败:', response);
      }
      console.log('取消点踩成功:', response);
    } else {
      // 如果之前未点踩，则添加点踩
      item.dislikeCount += 1;
      // 发送API请求
      const response = await dislikeShare(item.id, misId);
      // 检查响应状态，允许code=200或status=0表示成功
      // 同时允许status=300（用户已点踩）也算作成功
      const isSuccess = response && (response.code === 200 || response.status === 0 || response.status === 300);
      if (!isSuccess) {
        // 如果请求失败，恢复原状态
        item.isDisliked = originalIsDisliked;
        item.dislikeCount = originalDislikeCount;
        console.error('点踩失败:', response);
      }
      console.log('点踩成功:', response);

      // 如果同时点了赞，则先取消点赞再点踩
      if (item.isLiked) {
        // 先更新UI状态
        const originalLikeCount = item.likeCount;
        item.isLiked = false;
        item.likeCount = Math.max(0, item.likeCount - 1);

        // 发送API请求取消点赞
        try {
          await unlikeShare(item.id, misId);
        } catch (error) {
          console.error('取消点赞失败:', error);
          // 如果取消点赞失败，不恢复状态，因为点踩已经成功
        }
      }
    }
  } catch (error) {
    console.error('点踩操作失败:', error);
  }
};

// 跳转到搜索页面
const goToSearch = () => {
  // 保存当前滚动位置
  if (scrollableContent.value) {
    scrollPosition.value = scrollableContent.value.scrollTop;
    localStorage.setItem('shareScrollPosition', scrollPosition.value.toString());
  }
  void router.push({
    name: RouteName.SHARE_SEARCH,
  });
};

// 在组件挂载前调用
setInitialScrollPosition();

// 当组件被激活时（从缓存中恢复）
onActivated(() => {
  console.log('组件激活，准备恢复滚动位置:', scrollPosition.value);

  // 如果是初次渲染，则不设置滚动位置
  if (isInitialRender.value) {
    isInitialRender.value = false;
    return;
  }

  // 立即设置滚动位置，不使用延时
  if (scrollableContent.value) {
    scrollableContent.value.scrollTop = scrollPosition.value;
    console.log('立即设置滚动位置:', scrollPosition.value);
  }

  // 使用延时作为备用方案，确保滚动位置被设置
  setTimeout(() => {
    if (scrollableContent.value && scrollableContent.value.scrollTop !== scrollPosition.value) {
      scrollableContent.value.scrollTop = scrollPosition.value;
      console.log('通过延时设置滚动位置:', scrollPosition.value);
    }
  }, 50);
});

// 当组件被停用时（离开该页面时）
onDeactivated(() => {
  // 注意：在这里不再保存滚动位置
  // 因为我们发现在这个时间点滚动位置已经被重置为0
  // 我们依赖点击卡片时保存的滚动位置
  console.log('组件停用，使用已保存的滚动位置:', localStorage.getItem('shareScrollPosition'));
});

onUnmounted(() => {
  // 清理观察器
  if (observer) {
    observer.disconnect();
    console.log('观察器已断开连接');
  }
});
</script>

<style lang="scss" scoped>
.add-chat {
  width: 68px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: box-shadow 0.18s;
  img {
    width: 40px;
    height: 40px;
    pointer-events: none;
  }
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
  }
}

.header {
  height: 88px;
  padding: 10px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 32px;
  min-width: 168px;
}

.add-chat {
  width: 68px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  transition: box-shadow 0.18s;
  img {
    width: 40px;
    height: 40px;
    pointer-events: none;
  }
}

.chat-voice {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 68px;
  height: 68px;
  border-radius: 50%;
  background: #fff;
  flex-shrink: 0;
  pointer-events: none;
  visibility: hidden;
}

/* 初始滚动位置变量 */
:root {
  --initial-scroll-top: 0px;
}

.app-container {
  width: 100%;
  max-width: 750px; /* 移动端应用标准宽度 */
  height: 100vh;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.v-chat-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  max-width: 750px;
  margin: 0 auto;
}

.v-chat {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url(@/assets/img/page-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.content-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.scrollable-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 100px; /* 保留100px给底部TabBar */
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 20px;
}

.scrollable-content::-webkit-scrollbar {
  display: none;
}

.header {
  height: 88px;
  padding: 10px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .header-left,
  .header-right {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;

    .placeholder {
      width: 40px;
      height: 40px;
    }

    .user-avatar {
      width: 68px;
      height: 68px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #fff;
      font-size: 24px;
      font-weight: bold;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden; /* 确保内容不超出圆形边界 */
      flex-shrink: 0; /* 防止头像被挤压 */
      background: #e5e5e5;
      position: relative;

      &.loading-avatar {
        background: #e5e5e5;
      }

      .loading-dot {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #bbb;
        animation: avatar-loading-bounce 1s infinite alternate;
        display: inline-block;
      }

      &:active {
        opacity: 0.9;
      }
    }

    @keyframes avatar-loading-bounce {
      0% {
        transform: scale(1);
      }
      100% {
        transform: scale(1.3);
      }
    }
  }

  // 切换按钮样式
  .tab-switch {
    display: flex;
    background-color: rgba(255, 255, 255, 0.55);
    border-radius: 30px;
    padding: 5px;
    width: 220px !important;
    height: 54px !important;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden;
    box-sizing: border-box;
    max-width: 220px;
    max-height: 54px;
    min-width: 220px;
    min-height: 54px;

    .tab-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: 500;
      color: #666;
      position: relative;
      z-index: 2;
      transition: color 0.3s;
      border-radius: 25px;
      box-sizing: border-box;

      &.active {
        color: #333;
        font-weight: 600;
        background-color: #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.share-container {
  padding: 20px 25px 20px;
  flex: 1;
  overflow: auto;
  background-color: transparent;
  margin-bottom: 10px;

  .waterfall-container {
    display: flex;
    justify-content: space-between;

    .waterfall-column {
      width: 49%;

      .waterfall-item {
        margin-bottom: 15px;
        background-color: #fff;
        border-radius: 13px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .item-image {
          width: 100%;
          position: relative;
          overflow: hidden;

          img {
            width: 100%;
            display: block;
            object-fit: cover;
            object-position: center;
          }
        }

        .item-content {
          padding: 20px;

          .item-title {
            font-size: 30px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .item-text {
            font-size: 26px;
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
            max-height: calc(26px * 1.4 * 2); /* 字体大小 * 行高 * 行数 */
          }

          .item-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .user-info {
              display: flex;
              align-items: center;
              flex: 1;

              .avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 22px;
                font-weight: 800;
                margin-right: 10px;
              }

              .username {
                font-size: 21px;
                color: #666;
                font-weight: 370;
                max-width: 140px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
              }
            }

            .action-buttons {
              display: flex;
              align-items: center;
              gap: 3px; // 按钮之间的间距
            }

            .like-container,
            .dislike-container {
              display: flex;
              align-items: center;
              cursor: pointer;
              padding: 5px;
              min-width: 30px;
              justify-content: flex-end;

              &:hover .like-icon,
              &:hover .dislike-icon {
                transform: scale(1.05);
              }
              &:active .like-icon,
              &:active .dislike-icon {
                transform: scale(0.8);
                transition: transform 0.1s ease;
              }

              .like-icon {
                width: 26px;
                height: 26px;
                display: inline-block;
                background-color: #999;
                -webkit-mask: url('~@/assets/img/like.svg') no-repeat center/contain;
                mask: url('~@/assets/img/like.svg') no-repeat center/contain;
                transition: all 0.2s;
                vertical-align: middle;
                margin-right: 4px;
              }
              .like-icon.liked {
                background-color: #ff2222;
              }
              .dislike-icon {
                width: 26px;
                height: 26px;
                display: inline-block;
                background-color: #999;
                -webkit-mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
                mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
                transition: all 0.2s;
                vertical-align: middle;
                margin-right: 4px;
              }
              .dislike-icon.disliked {
                background-color: #303030;
              }
              .like-count {
                font-size: 18px;
                color: #666;
                min-width: 16px;
                text-align: right;
                transition: all 0.2s ease;
                font-weight: 600;
                margin-left: 0;
              }
              .like-icon.liked + .like-count {
                color: #ff2222;
              }

              .dislike-count {
                font-size: 18px;
                color: #666;
                min-width: 16px;
                text-align: right;
                transition: all 0.2s ease;
                font-weight: 600;
                margin-left: 0;
              }
              .dislike-icon.disliked + .dislike-count {
                color: #303030;
              }
            }

            @keyframes likeAnimation {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.2);
              }
              100% {
                transform: scale(1);
              }
            }

            .like-info {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #999;

              i {
                margin-right: 2px;
              }
            }
          }
        }

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #50a576;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 12px;
    }

    span {
      font-size: 25px;
      color: #666;
      font-weight: 500;
    }
  }

  .observer-target {
    height: 20px;
    width: 100%;
    margin-bottom: 20px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 隐藏 WebKit 滚动条 */
.v-chat::-webkit-scrollbar {
  display: none;
}
</style>
