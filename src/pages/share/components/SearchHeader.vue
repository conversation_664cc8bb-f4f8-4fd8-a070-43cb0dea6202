<template>
  <div class="search-header">
    <div class="back-icon" @click="goBack">
      <svg
        t="1713860512853"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="4123"
        width="25"
        height="25"
      >
        <path
          d="M671.968 912c-12.288 0-24.576-4.672-33.952-14.048L286.048 545.984c-18.752-18.72-18.752-49.12 0-67.872l351.968-352c18.752-18.752 49.12-18.752 67.872 0 18.752 18.72 18.752 49.12 0 67.872l-318.016 318.048 318.016 318.016c18.752 18.752 18.752 49.12 0 67.872-9.344 9.408-21.632 14.08-33.92 14.08z"
          fill="#34c37e"
          p-id="4124"
        ></path>
      </svg>
    </div>
    <div class="search-input-container">
      <input
        ref="searchInputRef"
        v-model="keyword"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        @keyup.enter="handleSearch"
      />
    </div>
    <div class="search-button" @click="handleSearch">搜索</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'SearchHeader',
  props: {
    initialKeyword: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '搜索内容',
    },
    autoFocus: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['search'],
  setup(props, { emit }) {
    const router = useRouter();
    const keyword = ref(props.initialKeyword);
    const searchInputRef = ref<HTMLInputElement | null>(null);

    // 返回上一页
    const goBack = () => {
      router.back();
    };

    // 处理搜索
    const handleSearch = () => {
      if (!keyword.value.trim()) {
        return;
      }

      emit('search', keyword.value.trim());
    };

    // 组件挂载后自动聚焦输入框
    onMounted(() => {
      if (props.autoFocus && searchInputRef.value) {
        searchInputRef.value.focus();
      }
    });

    return {
      keyword,
      searchInputRef,
      goBack,
      handleSearch,
    };
  },
});
</script>

<style lang="scss" scoped>
.search-header {
  display: flex;
  align-items: center;
  padding: 15px 25px;
  background-color: transparent;
  box-shadow: transparent;

  .back-icon {
    cursor: pointer;
    padding: 10px;
    margin-right: 10px;
    border-radius: 50%;
  }

  .search-input-container {
    flex: 1;
    margin-right: 15px;

    .search-input {
      width: 100%;
      height: 60px;
      border: 1px solid transparent;
      background-color: rgba(255, 255, 255, 0.75);
      border-radius: 30px;
      padding: 0 20px;
      font-size: 25px;
      outline: none;

      &:focus {
        border-color: #34c37e;
        box-shadow: 0 0 0 2px rgba(52, 195, 124, 0.2);
      }
    }
  }

  .search-button {
    padding: 10px 20px;
    background: linear-gradient(150deg, #41d18b 0%, #79e4e8 100%);
    color: white;
    border-radius: 30px;
    font-size: 25px;
    cursor: pointer;

    &:hover {
      background-color: #3a80d2;
    }
  }
}
</style>
