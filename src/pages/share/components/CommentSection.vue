<template>
  <div class="comment-section">
    <!-- 评论数量 -->
    <div v-if="comments" class="comment-count">共 {{ commentCount }} 条评论</div>
    <div v-else class="comment-count">暂无评论</div>

    <!-- 评论列表 -->
    <div v-if="comments && comments.length > 0" class="comment-list">
      <!-- 动态渲染评论列表 -->
      <div
        v-for="comment in comments"
        :key="comment.comment_id"
        :class="['comment-item', comment.comment_parent_id ? 'comment-reply' : '']"
        @click="checkIfOwnComment(comment)"
      >
        <div
          v-if="comment.comment_mis_id"
          class="comment-avatar"
          :style="{ backgroundColor: getRandomColor(comment.comment_mis_id) }"
          style="cursor: pointer"
          @click.stop="goToUserProfile(comment.comment_mis_id)"
        >
          {{ getAvatarLetter(comment.comment_mis_id) }}
        </div>
        <div v-else class="comment-avatar avatar-skeleton"></div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="comment-username">{{ comment.comment_mis_id }}</span>
            <span class="comment-time">{{ formatTime(comment.create_time) }}</span>
          </div>
          <div class="comment-text">
            <!-- 如果是回复评论，显示回复对象 -->
            <template v-if="comment.comment_parent_id && getParentUsername(comment)">
              <span class="reply-to">
                <span class="reply-word">回复</span>
                <span class="reply-name">{{ getParentUsername(comment) }}</span
                >：
              </span>
            </template>
            {{ comment.comment_content }}
            <span class="reply-button" @click.stop="replyToComment(comment)">回复</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 无评论时显示 -->
    <div v-else-if="comments && comments.length === 0" class="no-comments">暂无评论，快来发表第一条评论吧！</div>

    <!-- 底部操作菜单 -->
    <ActionSheet
      :visible="showActionSheet"
      title="评论操作"
      :actions="actionSheetActions"
      @select="handleActionSelect"
      @cancel="closeActionSheet"
    />

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="delete-confirm-overlay" @click="cancelDelete">
      <div class="delete-confirm-dialog" @click.stop>
        <div class="delete-confirm-title">删除评论</div>
        <div class="delete-confirm-content">确定要删除这条评论吗？</div>
        <div class="delete-confirm-buttons">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="confirm-btn" @click="confirmDelete">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { RouteName } from '@/constants/route-name';
import { ICommentItem } from '@/apis/share';
import { getUserInfo } from '@/apis/common';
import { getRandomColor, getAvatarLetter } from '@/utils/avatar';
import ActionSheet, { IActionItem } from './ActionSheet.vue'; // 导入组件和接口类型

const props = defineProps({
  comments: {
    type: Array as () => ICommentItem[],
    default: () => [],
  },
  commentCount: {
    type: Number,
    default: 0,
  },
  authorId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['reply', 'delete-comment']);

// 获取路由实例
const router = useRouter();

// 状态管理
const currentComment = ref<ICommentItem | null>(null);
const showDeleteConfirm = ref(false);
const showActionSheet = ref(false);

// 底部操作菜单选项
const actionSheetActions = ref<IActionItem[]>([{ text: '删除评论', value: 'delete', color: '#ff4d4f' }]);

// 检查是否是自己的评论，如果是则显示操作菜单
const checkIfOwnComment = async (comment: ICommentItem) => {
  currentComment.value = comment;

  try {
    // 获取当前用户ID
    const userInfo = await getUserInfo();
    const currentUserId = userInfo?.login || '';

    // 只有评论作者或帖子作者才能删除评论
    if (currentUserId === comment.comment_mis_id || currentUserId === props.authorId) {
      // 显示操作菜单
      showActionSheet.value = true;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  currentComment.value = null;
};

// 确认删除
const confirmDelete = () => {
  if (currentComment.value) {
    emit('delete-comment', currentComment.value);
    showDeleteConfirm.value = false;
    currentComment.value = null;
  }
};

// 处理底部操作菜单选项选择
const handleActionSelect = (action: IActionItem) => {
  showActionSheet.value = false;

  if (action.value === 'delete') {
    showDeleteConfirm.value = true;
  }
};

// 关闭底部操作菜单
const closeActionSheet = () => {
  showActionSheet.value = false;
};

// 回复评论的方法
const replyToComment = (comment: ICommentItem) => {
  // 发送事件到父组件，通知底部输入框更新，带上comment_id
  emit('reply', {
    commentId: comment.comment_id,
    username: comment.comment_mis_id,
  });
};

// 格式化评论时间
const formatTime = (timeString: string) => {
  if (!timeString) return '';

  const now = new Date();
  const commentTime = new Date(timeString);
  const diffMs = now.getTime() - commentTime.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return '刚刚';
  }
  if (diffMin < 60) {
    return `${diffMin}分钟前`;
  }
  if (diffHour < 24) {
    return `${diffHour}小时前`;
  }
  if (diffDay < 30) {
    return `${diffDay}天前`;
  }
  const year = commentTime.getFullYear();
  const month = String(commentTime.getMonth() + 1).padStart(2, '0');
  const day = String(commentTime.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取父评论的用户名
const getParentUsername = (comment: ICommentItem) => {
  if (!comment.comment_parent_id || !props.comments) return '';

  const parentComment = props.comments.find((c) => c.comment_id === comment.comment_parent_id);
  return parentComment ? parentComment.comment_mis_id : '';
};

// 跳转到用户主页
const goToUserProfile = (userId: string) => {
  if (userId) {
    void router.push({
      name: RouteName.USER_PROFILE,
      params: { id: userId },
    });
  }
};
</script>

<style scoped lang="scss">
.no-comments {
  text-align: center;
  color: #999;
  font-size: 24px;
  margin-top: 50px;
}

.comment-section {
  margin-top: 25px;
  padding-top: 20px;
  padding-bottom: 70px; // 为底部交互栏留出空间
  border-top: 1px solid #f0f0f0;

  .comment-count {
    font-size: 26px;
    color: #333;
    font-weight: 500;
    margin-bottom: 15px;
    text-align: left;
  }

  .comment-input-area {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;

    .comment-avatar {
      width: 55px;
      height: 55px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 25px;
      flex-shrink: 0;
      font-weight: 500;
      position: relative;
    }

    .avatar-skeleton {
      background: linear-gradient(90deg, #ececec 25%, #f5f5f5 50%, #ececec 75%);
      background-size: 200% 100%;
      animation: avatar-skeleton-loading 1.2s infinite linear;
      width: 55px;
      height: 55px;
      border-radius: 50%;
      display: inline-block;
    }

    @keyframes avatar-skeleton-loading {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }

    .comment-input-wrapper {
      flex: 1;

      .comment-input {
        width: 100%;
        height: 55px;
        border: 1px solid #e0e0e0;
        border-radius: 30px;
        padding: 0 25px;
        font-size: 20px;
        outline: none;

        &:focus {
          border-color: #4ecdc4;
        }

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .comment-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .comment-item {
    display: flex;
    align-items: flex-start;
    padding: 10px 0;

    &.comment-reply {
      margin-left: 40px;
      position: relative;
    }

    .comment-avatar {
      width: 55px;
      height: 55px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 25px;
      flex-shrink: 0;
      margin-right: 10px;
      font-weight: 500;
    }

    .comment-content {
      flex: 1;

      .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        .comment-username {
          font-size: 28px;
          color: #999;
          font-weight: 400;
        }

        .comment-time {
          font-size: 22px;
          color: #999;
        }
      }

      .comment-text {
        font-size: 30px;
        color: #333;
        line-height: 1.7;
        word-break: break-word;
        position: relative;

        .reply-to {
          font-weight: 400;
          margin-right: 5px;

          .reply-word {
            color: #333;
          }

          .reply-name {
            font-size: 26px;
            color: #999;
            font-weight: 400;
          }
        }

        .reply-button {
          color: #999;
          margin-right: 18px;
          cursor: pointer;
          font-size: 24px;
          display: inline-block;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .delete-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    max-width: 750px;
    margin: 0 auto;
  }

  .delete-confirm-dialog {
    width: 100%;
    max-width: 500px;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .delete-confirm-title {
    padding: 20px 15px;
    font-size: 25px;
    font-weight: 500;
    text-align: center;
    border-bottom: 1px solid #eee;
  }

  .delete-confirm-content {
    padding: 30px 20px;
    text-align: center;
    font-size: 25px;
    color: #333;
  }

  .delete-confirm-buttons {
    display: flex;
    border-top: 1px solid #eee;

    button {
      flex: 1;
      padding: 12px 0;
      font-size: 25px;
      border: none;
      background: none;
      outline: none;

      &.cancel-btn {
        color: #666;
        border-right: 1px solid #eee;
      }

      &.confirm-btn {
        color: #ff6b6b;
        font-weight: 500;
      }
    }
  }
}
</style>
