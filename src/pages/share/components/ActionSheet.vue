<template>
  <div v-if="visible" class="action-sheet-container" @click="onCancel">
    <div class="action-sheet" @click.stop>
      <div class="action-sheet-header">
        <div class="action-sheet-title">{{ title }}</div>
      </div>
      <div class="action-sheet-content">
        <div
          v-for="(action, index) in actions"
          :key="index"
          class="action-sheet-item"
          :style="{ color: action.color || '#333' }"
          @click="onSelect(action)"
        >
          <i v-if="action.icon" :class="['action-sheet-icon', action.icon]"></i>
          <span class="action-sheet-text">{{ action.text }}</span>
        </div>
      </div>
      <div class="action-sheet-footer">
        <div class="action-sheet-cancel" @click="onCancel">取消</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

export interface IActionItem {
  text: string;
  icon?: string;
  value: string;
  color?: string;
}

export default defineComponent({
  name: 'ActionSheet',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '请选择操作',
    },
    actions: {
      type: Array as PropType<IActionItem[]>,
      default: () => [],
    },
  },
  emits: ['select', 'cancel'],
  setup(props, { emit }) {
    const onSelect = (action: IActionItem) => {
      emit('select', action);
    };

    const onCancel = () => {
      emit('cancel');
    };

    return {
      onSelect,
      onCancel,
    };
  },
});
</script>

<style scoped lang="scss">
.action-sheet-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: fadeIn 0.2s ease-in-out;
  width: 100%;
  max-width: 750px;
  margin: 0 auto;
}

.action-sheet {
  background-color: #fff;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  transform: translateY(0);
  animation: slideUp 0.3s ease-in-out;
  width: 100%;
}

.action-sheet-header {
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.action-sheet-title {
  font-size: 25px;
  font-weight: 500;
  color: #333;
}

.action-sheet-content {
  max-height: 60vh;
  overflow-y: auto;
}

.action-sheet-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  font-size: 25px;
  border-bottom: 1px solid #f5f5f5;
}

.action-sheet-icon {
  margin-right: 10px;
  font-size: 25px;
}

.action-sheet-text {
  font-weight: 400;
}

.action-sheet-footer {
  padding: 15px 0;
  border-top: 8px solid #f5f5f5;
}

.action-sheet-cancel {
  text-align: center;
  font-size: 25px;
  color: #333;
  padding: 10px 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
