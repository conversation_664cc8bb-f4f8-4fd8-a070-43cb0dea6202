<template>
  <div class="interaction-bar">
    <div class="bar-content">
      <div class="input-container" :class="{ 'input-focused': isInputFocused }">
        <div class="input-wrapper">
          <input
            ref="commentInputRef"
            v-model="commentText"
            type="text"
            class="comment-input"
            :placeholder="replyTo ? `回复@${replyTo}` : '说点什么...'"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @keyup.enter="submitComment"
          />
          <div class="buttons-container">
            <button
              v-if="commentText.trim()"
              class="send-btn"
              aria-label="发送"
              @mousedown.prevent="submitComment($event)"
            >
              <svg viewBox="0 0 24 24" width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 21L21 12L3 3V10L17 12L3 14V21Z" fill="#fff" />
              </svg>
            </button>
            <span
              v-if="replyTo || commentText.trim()"
              class="cancel-reply"
              @mousedown.prevent="handleCancelReplyOrClear"
            >
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="3.2" y1="3.2" x2="8.8" y2="8.8" stroke="#fff" stroke-width="2.4" stroke-linecap="round" />
                <line x1="8.8" y1="3.2" x2="3.2" y2="8.8" stroke="#fff" stroke-width="2.4" stroke-linecap="round" />
              </svg>
            </span>
          </div>
        </div>
      </div>
      <div v-if="!isInputFocused" class="action-buttons">
        <!-- 点赞图标 -->
        <div class="action-button" @click="handleLikeClick" @dblclick="handleLikeDoubleClick">
          <span class="like-icon" :class="{ liked: isLiked }"></span>
          <span :class="{ 'active-count': isLiked }">{{ likeCount }}</span>
        </div>
        <!-- 点踩图标 -->
        <div class="action-button" @click="handleDislikeClick" @dblclick="handleDislikeDoubleClick">
          <span class="dislike-icon" :class="{ disliked: isDisliked }"></span>
          <span :class="{ 'active-dislike-count': isDisliked }">{{ dislikeCount }}</span>
        </div>
        <!-- 评论图标 -->
        <div class="action-button">
          <span class="comment-icon"></span>
          <span>{{ commentCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, PropType } from 'vue';

export default defineComponent({
  name: 'InteractionBar',
  props: {
    // 接收回复对象的用户名
    replyTo: {
      type: String,
      default: '',
    },
    // 新增：父评论id
    replyToParentId: {
      type: String,
      default: null,
    },
    // 点赞数
    likeCount: {
      type: Number,
      default: 0,
    },
    // 点踩数
    dislikeCount: {
      type: Number,
      default: 0,
    },
    // 评论数
    commentCount: {
      type: Number,
      default: 0,
    },
    // 是否已点赞
    isLiked: {
      type: Boolean,
      default: false,
    },
    // 是否已点踩
    isDisliked: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['submit-comment', 'cancel-reply', 'toggle-like', 'toggle-dislike'],
  setup(props, { emit }) {
    // 评论文本
    const commentText = ref('');

    // 记录最后一次点击时间，用于判断是否是双击
    const lastLikeClickTime = ref(0);
    const lastDislikeClickTime = ref(0);
    const doubleClickDelay = 300; // 双击判定的时间间隔（毫秒）

    // 提交评论
    const submitComment = () => {
      if (!commentText.value.trim()) return;

      // 发送评论事件到父组件
      emit('submit-comment', {
        text: commentText.value,
        replyTo: props.replyTo,
        replyToParentId: props.replyToParentId,
      });

      // 清空评论框
      commentText.value = '';
    };

    // 取消回复
    const cancelReply = () => {
      emit('cancel-reply');
    };

    // 处理点赞点击
    const handleLikeClick = () => {
      const now = Date.now();
      // 如果不是双击，则触发点赞事件
      if (now - lastLikeClickTime.value > doubleClickDelay) {
        // 记录点击时间，等待可能的第二次点击
        lastLikeClickTime.value = now;
        setTimeout(() => {
          // 如果没有双击，则触发普通点击事件
          if (Date.now() - lastLikeClickTime.value >= doubleClickDelay) {
            toggleLike();
          }
        }, doubleClickDelay);
      }
    };

    // 处理点赞双击
    const handleLikeDoubleClick = () => {
      lastLikeClickTime.value = Date.now();
      // 如果已经点赞，则不做任何操作
      if (!props.isLiked) {
        toggleLike();
      }
    };

    // 处理点踩点击
    const handleDislikeClick = () => {
      const now = Date.now();
      // 如果不是双击，则触发点踩事件
      if (now - lastDislikeClickTime.value > doubleClickDelay) {
        // 记录点击时间，等待可能的第二次点击
        lastDislikeClickTime.value = now;
        setTimeout(() => {
          // 如果没有双击，则触发普通点击事件
          if (Date.now() - lastDislikeClickTime.value >= doubleClickDelay) {
            toggleDislike();
          }
        }, doubleClickDelay);
      }
    };

    // 处理点踩双击
    const handleDislikeDoubleClick = () => {
      lastDislikeClickTime.value = Date.now();
      // 如果已经点踩，则不做任何操作
      if (!props.isDisliked) {
        toggleDislike();
      }
    };

    // 切换点赞状态
    const toggleLike = () => {
      emit('toggle-like');
    };

    // 切换点踩状态
    const toggleDislike = () => {
      emit('toggle-dislike');
    };

    const isInputFocused = ref(false);
    const commentInputRef = ref<HTMLInputElement | null>(null);

    // 输入框聚焦时
    const handleInputFocus = () => {
      isInputFocused.value = true;
    };
    // 输入框失焦时
    const handleInputBlur = () => {
      isInputFocused.value = false;
    };
    // 发送评论后自动失焦（但按钮点击时先发送再失焦，防止因失焦丢失点击）
    const unfocusInput = () => {
      if (commentInputRef.value) {
        commentInputRef.value.blur();
      }
    };
    // 发送评论，按钮和回车统一逻辑
    const submitCommentWithUnfocus = (e?: Event) => {
      if (!commentText.value.trim()) return;
      emit('submit-comment', {
        text: commentText.value,
        replyTo: props.replyTo,
        replyToParentId: props.replyToParentId,
      });
      commentText.value = '';
      // 只有回车或主动调用时失焦，按钮点击时延迟失焦
      if (e && e.type === 'click') {
        setTimeout(() => {
          unfocusInput();
        }, 0);
      } else {
        unfocusInput();
      }
    };

    // 统一处理：如果有回复状态则取消回复，否则清空输入并失焦
    const handleCancelReplyOrClear = () => {
      if (props.replyTo) {
        cancelReply();
      } else {
        commentText.value = '';
        if (commentInputRef.value) {
          commentInputRef.value.blur();
        }
      }
    };

    return {
      commentText,
      commentInputRef,
      isInputFocused,
      props,
      handleInputFocus,
      handleInputBlur,
      submitComment: submitCommentWithUnfocus,
      handleLikeClick,
      handleLikeDoubleClick,
      handleCancelReplyOrClear,
      handleDislikeClick,
      handleDislikeDoubleClick,
      toggleLike,
      toggleDislike,
    };
  },
});
</script>

<style scoped lang="scss">
.interaction-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  max-width: 750px;
  height: 110px;
  margin: 0 auto;
  background-color: #fff;
  z-index: 9999;
  box-shadow: 0 -2px 18px 0 rgba(0, 0, 0, 0.08);
  pointer-events: auto;

  .bar-content {
    width: 100%;
    height: 100%;
    padding: 0 25px; /* 两侧边距加大 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .input-container {
    flex: 1;
    margin-right: 30px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;

    &.input-focused {
      flex: 1 1 100%;
      margin-right: 0;
      width: 100%;
      z-index: 2;
    }

    .input-wrapper {
      position: relative;
      width: 100%;
      display: flex;
      align-items: center;
    }

    .comment-input {
      flex: 1;
      width: 100%;
      height: 75px;
      border: 0px solid #e0e0e0;
      border-radius: 38px;
      padding: 0 32px;
      padding-right: 100px; /* 为按钮区域留出更多空间 */
      font-size: 28px;
      outline: none;
      background-color: #f5f5f5;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:focus {
        background-color: #f5f5f5;
        box-shadow: none;
        border-color: transparent;
      }
      &::placeholder {
        color: #999;
      }
    }

    .buttons-container {
      position: absolute;
      right: 20px; /* 增加与边界的距离 */
      display: flex;
      align-items: center;
      gap: 10px; /* 按钮间距稍微增加 */
    }

    .send-btn {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #111;
      border: none;
      outline: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
      padding: 0;
      transition: background 0.2s;
      z-index: 3;

      svg {
        width: 20px;
        height: 20px;
        display: block;
        margin-left: 4px;
      }
      &:active {
        background: #333;
      }
    }

    .cancel-reply {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: #ccc;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      cursor: pointer;
      z-index: 10;

      &:hover {
        background-color: #999;
      }
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 18px;
    transition: opacity 0.2s;

    &.hide-actions {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }

    .action-button {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      .like-icon {
        width: 34px;
        height: 34px;
        display: inline-block;
        background-color: #999; // 默认灰色加深
        -webkit-mask: url('~@/assets/img/like.svg') no-repeat center/contain;
        mask: url('~@/assets/img/like.svg') no-repeat center/contain;
        transition: all 0.2s;
        vertical-align: middle;
      }
      .like-icon.liked {
        background-color: #ff2222; // 选中主色
      }
      .dislike-icon {
        width: 34px;
        height: 34px;
        display: inline-block;
        background-color: #999; // 默认灰色加深
        -webkit-mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
        mask: url('~@/assets/img/dislike.svg') no-repeat center/contain;
        transition: all 0.2s;
        vertical-align: middle;
      }
      .dislike-icon.disliked {
        background-color: #303030; // 选中主色（绿色更醒目）
      }

      .comment-icon {
        width: 34px;
        height: 34px;
        display: inline-block;
        background-color: #999;
        -webkit-mask: url('~@/assets/img/comment.svg') no-repeat center/contain;
        mask: url('~@/assets/img/comment.svg') no-repeat center/contain;
        transition: all 0.2s;
        vertical-align: middle;
      }

      &:hover .like-icon,
      &:hover .dislike-icon {
        transform: scale(1.12);
      }
      &:active .like-icon,
      &:active .dislike-icon {
        transform: scale(0.92);
      }

      span {
        font-size: 28px;
        color: #666;
        min-width: 28px;
        text-align: center;
        &.active-count {
          color: #ff2222;
          font-weight: 600;
        }
        &.active-dislike-count {
          color: #303030;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
