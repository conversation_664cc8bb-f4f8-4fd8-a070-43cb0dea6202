<template>
  <div class="tab-bar-container">
    <div class="tab-bar">
      <div class="tab-item" :class="{ active: activeTab === 'home' }" @click="switchTab('home')">
        <div class="icon-container">
          <div class="tab-icon home-icon"></div>
        </div>
        <div v-if="activeTab === 'home'" class="glow-circle"></div>
        <div class="tab-label">首页</div>
      </div>
      <!--
      <div class="tab-item" :class="{ active: activeTab === 'recommend' }" @click="switchTab('recommend')">
        <div class="icon-container">
          <div class="tab-icon recommend-icon"></div>
        </div>
        <div v-if="activeTab === 'recommend'" class="glow-circle"></div>
        <div class="tab-label">推荐</div>
      </div>
      -->
      <div class="tab-item" :class="{ active: activeTab === 'message' }" @click="switchTab('message')">
        <div class="icon-container" style="position: relative">
          <div class="tab-icon message-icon"></div>
          <!-- 未读消息数量
          <span v-if="unreadCount > 0" class="tab-badge">
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </span>
           -->
        </div>
        <div v-if="activeTab === 'message'" class="glow-circle"></div>
        <div class="tab-label">消息</div>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'profile' }" @click="switchTab('profile')">
        <div class="icon-container">
          <div class="tab-icon profile-icon"></div>
        </div>
        <div v-if="activeTab === 'profile'" class="glow-circle"></div>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { RouteName } from '@/constants/route-name';
import { getUserInfo } from '@/apis/common';
import { getUnreadCounts } from '@/apis/message';

export default defineComponent({
  name: 'TabBar',
  props: {
    defaultTab: {
      type: String,
      default: 'home',
    },
  },
  setup(props) {
    const router = useRouter();
    const route = useRoute();
    const activeTab = ref(props.defaultTab);
    const currentMisId = ref('');
    const unreadCount = ref(0);

    // 获取当前用户信息
    const fetchCurrentUser = async () => {
      try {
        const userInfo = await getUserInfo();
        currentMisId.value = userInfo.login || '';
        // 保存到本地存储，以便在个人页面使用
        localStorage.setItem('currentMisId', currentMisId.value);
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取失败，尝试从 localStorage 中获取
        const storedMisId = localStorage.getItem('currentMisId');
        if (storedMisId) {
          currentMisId.value = storedMisId;
        }
      }
    };

    // 获取未读消息数量
    const fetchUnreadCount = async () => {
      if (!currentMisId.value) return;
      try {
        const res = await getUnreadCounts(currentMisId.value);
        if (res.status === 0 && res.data) {
          const total = (res.data.unread_like_count || 0) + (res.data.unread_comment_count || 0);
          unreadCount.value = total;
        } else {
          unreadCount.value = 0;
        }
      } catch (e) {
        unreadCount.value = 0;
      }
    };

    // 组件挂载时获取用户信息和未读数量
    onMounted(() => {
      void fetchCurrentUser();
      // 由于currentMisId异步获取，延迟拉取未读数
      setTimeout(fetchUnreadCount, 400);
    });
    // 用户ID变化时重新拉取未读数
    watch(currentMisId, () => {
      void fetchUnreadCount();
    });

    // 根据当前路由路径设置活动标签
    const updateActiveTab = (path: string) => {
      if (path.includes('/recommend')) {
        activeTab.value = 'recommend';
      } else if (path.includes('/share') && !path.includes('/user')) {
        activeTab.value = 'home';
      } else if (path.includes('/user')) {
        activeTab.value = 'profile';
      }
    };

    // 监听路由变化
    watch(
      () => route.path,
      (newPath) => {
        updateActiveTab(newPath);
      },
      { immediate: true },
    );

    // 切换标签
    const switchTab = (tab: string) => {
      if (tab === activeTab.value) return;

      activeTab.value = tab;

      switch (tab) {
        case 'home':
          void router.push({ name: RouteName.SHARE });
          break;
        case 'recommend':
          // 跳转到推荐页面
          void router.push({ name: RouteName.RECOMMEND });
          break;
        case 'message':
          void router.push({ name: 'message' });
          break;
        case 'profile':
          // 跳转到个人页面，并携带用户ID
          if (currentMisId.value) {
            // 在跳转前先记录上一个页面
            localStorage.setItem('previousTab', 'home');
            void router.push({
              name: RouteName.USER_PROFILE,
              params: { id: currentMisId.value },
              query: { fromTab: 1 },
            });
          } else {
            // 如果没有用户ID，尝试从 localStorage 中获取
            const storedMisId = localStorage.getItem('currentMisId');
            if (storedMisId) {
              void router.push({
                name: RouteName.USER_PROFILE,
                params: { id: storedMisId },
                query: { fromTab: 1 },
              });
            } else {
              // 如果还是没有，则直接跳转到个人页面，由个人页面处理
              void router.push({ name: RouteName.USER_PROFILE, query: { fromTab: 1 } });
            }
          }
          break;
        default:
          // 默认情况下不做任何操作
          break;
      }
    };

    return {
      activeTab,
      switchTab,
      unreadCount,
    };
  },
});
</script>

<style lang="scss" scoped>
.tab-bar-container {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 100;
  width: 100%;
  max-width: 750px;
  margin: 0 auto;
}

.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 98%;
  max-width: 710px; // 与两个帖子并列的宽度一致
  height: 115px;
  background: #ffffff;
  border-radius: 35px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0 15px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 10px 0;
  cursor: pointer;
  transition: all 0.3s;

  &.active {
    .tab-icon {
      background-color: #34c37e;
      transform: scale(1.1);
    }

    .tab-label {
      color: #34c37e;
      font-weight: 500;
    }
  }
}

.tab-icon {
  width: 50px;
  height: 50px;
  background-color: #999;
  margin-bottom: 5px;
  transition: all 0.3s;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  -webkit-mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
  mask-position: center;
  mask-repeat: no-repeat;
  mask-size: contain;
  position: relative;
  z-index: 1;
}

.home-icon {
  -webkit-mask-image: url('~@/assets/img/home.svg');
  mask-image: url('~@/assets/img/home.svg');
}

.recommend-icon {
  -webkit-mask-image: url('~@/assets/img/fanli.svg');
  mask-image: url('~@/assets/img/fanli.svg');
}

.message-icon {
  -webkit-mask-image: url('~@/assets/img/message.svg');
  mask-image: url('~@/assets/img/message.svg');
}

.profile-icon {
  -webkit-mask-image: url('~@/assets/img/mine.svg');
  mask-image: url('~@/assets/img/mine.svg');
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
}

.glow-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #34c3a2;
  opacity: 0.5;
  margin: 5px auto;
  box-shadow: 0 0 12px 5px rgba(83, 215, 151, 0.5);
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.tab-label {
  font-size: 24px;
  color: #666;
  margin-top: 2px;
  transition: all 0.3s;
}

.tab-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 22px;
  height: 22px;
  padding: 0 5px;
  background: linear-gradient(90deg, #ff6b6b 0%, #ff3c3c 100%);
  color: #fff;
  border-radius: 11px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.18);
  line-height: 1;
  z-index: 2;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
@media (max-width: 600px) {
  .tab-badge {
    min-width: 18px;
    height: 18px;
    font-size: 12px;
    right: -4px;
    top: -4px;
  }
}
</style>
