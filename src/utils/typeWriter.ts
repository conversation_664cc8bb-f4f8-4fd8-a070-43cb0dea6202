export class Typewriter {
  private queue: string[] = [];

  private consuming = false;

  private timer: ReturnType<typeof setTimeout>;

  constructor(private onConsume: (str: string) => void) {}

  // 2s内打印完缓冲区的文字，打印速度根据缓冲区的文字多少决定，最慢200ms打印一次
  dynamicSpeed() {
    const speed = 2000 / this.queue.length;
    if (speed > 200) {
      return 200;
    }
    return Math.floor(speed);
  }

  // 添加字符串到队列
  add(str: string) {
    if (!str) return;
    const lastWord = this.queue[this.queue.length - 1];
    if (lastWord === str) {
      return;
    }
    this.queue.push(str);
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const str = this.queue.shift();
      if (str) {
        this.onConsume(str);
      }
    }
  }

  // 持续消费
  loopConsume() {
    this.consume();
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      if (this.consuming) {
        this.loopConsume();
      }
    }, this.dynamicSpeed());
  }

  // 开始消费队列
  start() {
    this.consuming = true;
    this.loopConsume();
  }

  // 结束消费队列
  done() {
    this.consuming = false;
    clearTimeout(this.timer);
    // 把queue中剩下的字符一次性消费
    if (this.queue.length) {
      this.onConsume(this.queue.pop() as string);
      this.queue = [];
    }
  }

  // 停止打印
  stop() {
    this.consuming = false;
    this.queue = [];
  }
}
