interface IContent {
  type: 'TEXT' | 'IMAGE';
  text?: string;
  mime?: string;
  data?: string;
  name?: string;
}
interface IUtterance {
  type: 'TEXT' | 'IMAGE' | 'AUDIO'; // 枚举值：TEXT、IMAGE、AUDIO
  content: string; // 文本内容或图片URL
}
interface IAsrParams {
  sessionId: string; // sessionId
  format: string; // 格式，pcm
  sampleRate: number; // 采样率，常见有8000、16000
  index: number; // 数据包序号，从1开始，以-1*Seq结束
  data: Blob | ArrayBuffer | null; // 二进制的音频数据
}
interface IAsrRequest {
  endTime: number;
  resIndex: number;
  sessionFinished: boolean;
  sessionId: string;
  speechTime: number;
  startTime: number;
  status: number;
  subSessionId: string;
  text: string;
  full_text: string;
}
interface ITopicRecommend {
  desc: string;
  icon: string;
  prompt: string;
  title: string;
}
// create接口入参
interface ICreateRequest {
  model: string;
  welcomeMessageSwitch: boolean;
  agentId: string;
}
// create接口出参
interface IConversation {
  conversationId: string;
  creatorMis: string;
  creatorName: string;
  title: string;
  model: string;
  messageCount: number | null;
  status: number;
  agentId: string;
  addTime: string | null;
  updateTime: string | null;
  appfactoryConversationId: string | null;
  conversationType: string | null;
  isEncryptionMode: boolean;
  agentInfo: IAgentInfo;
  welcomeMessageSwitch: boolean;
}

interface IAgentInfo {
  agentId: string;
  agentName: string;
  agentIconUrl: string;
  systemPrompt: string;
  promptMap: Record<string, string>;
  welcomeMessage: string;
  pluginList: string[];
  modelList: IModel[];
  modelNameList: string[];
  modelFileUploadButton: string[];
  defaultPlugin: string;
  defaultModel: string;
  description: string;
  sort: number;
  safeModeSupported: boolean;
  securityModelConfig: ISecurityModelConfig;
  fileUploadButton: boolean;
  generalAgentTypeEnum: string;
  fileUploadConfig: IFileUploadConfig;
}

interface IModel {
  label: string;
  model: string;
  description: string;
  maxTokenCount: number;
  imageUrl: string;
}

interface ISecurityModelConfig {
  pluginList: string[];
  modelNameList: string[];
  modelList: Model[];
  defaultModel: string;
  fileUploadSupported: boolean;
  safeAgentIconUrl: string;
}

interface IFileUploadConfig {
  format: string[];
  limitSize: number;
}
// 对话接口入参
interface IChatRequest {
  content: string;
  conversationId: string;
  model: string;
  regenerate: number;
  requireAction: boolean;
  userSelectPluginCode: string;
}
interface IChatMessage {
  id: string;
  created?: number;
  choices?: Choice[];
  content?: string;
  agentInfo?: AgentInfo;
  loadingStatus?: boolean;
  lastOne?: boolean;
  reasoning_content?: string;
  reasoning_status?: string;
}

interface IChoice {
  delta: unknown;
  index: number;
  finish_reason: string;
}

interface IChatSate {
  conversationInfo: IConversation;
  answerStatus: AnswerStatusEnum;
}
type Role = 'user' | 'assistant' | 'system';

interface IReasoningData {
  content: string;
  status: string;
}
interface IChatStreamContent {
  role: Role;
  content: string;
  key: number | string;
  isFinish: boolean;
  debugInfo?: IChatMessage;
  reasoningData: IReasoningData;
  pre_tools?: any[];
}
